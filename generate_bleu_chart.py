#!/usr/bin/env python3
"""
Genera grafico PNG con tutti i BLEU (1-4) per modelli baseline
"""

import matplotlib.pyplot as plt
import numpy as np
import json
from datetime import datetime

def create_bleu_chart():
    # Dati BLEU completi dai risultati baseline
    models = ['BLIP-2', 'Florence2', 'Idefics3']
    
    # Dati BLEU 1-4 per ogni modello
    bleu_data = {
        'BLIP-2': [0.2381, 0.1322, 0.0757, 0.0506],
        'Florence2': [0.2507, 0.1514, 0.0675, 0.0396], 
        'Idefics3': [0.1269, 0.1066, 0.0933, 0.0789]
    }
    
    # Configurazione grafico
    x = np.arange(4)  # BLEU-1, BLEU-2, BLEU-3, BLEU-4
    width = 0.25
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Colori per ogni modello
    colors = ['#e74c3c', '#3498db', '#2ecc71']
    
    # Crea le barre per ogni modello
    for i, (model, color) in enumerate(zip(models, colors)):
        offset = (i - 1) * width
        bars = ax.bar(x + offset, bleu_data[model], width, 
                     label=model, color=color, alpha=0.8)
        
        # Aggiungi valori sopra le barre
        for j, bar in enumerate(bars):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                   f'{height:.4f}', ha='center', va='bottom', 
                   fontweight='bold', fontsize=10)
    
    # Configurazione assi e titoli
    ax.set_xlabel('Metriche BLEU', fontsize=14, fontweight='bold')
    ax.set_ylabel('Score BLEU', fontsize=14, fontweight='bold')
    ax.set_title('📊 Confronto Completo BLEU-1, BLEU-2, BLEU-3, BLEU-4\nModelli Baseline SVG Captioning', 
                fontsize=16, fontweight='bold', pad=20)
    
    # Etichette asse X
    ax.set_xticks(x)
    ax.set_xticklabels(['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4'], fontsize=12)
    
    # Griglia e legenda
    ax.grid(True, alpha=0.3, linestyle='--')
    ax.legend(fontsize=12, loc='upper right')
    
    # Limiti asse Y
    ax.set_ylim(0, max(max(scores) for scores in bleu_data.values()) * 1.15)
    
    # Aggiungi informazioni aggiuntive
    info_text = (
        "Dataset: SVG-to-Caption con immagini colorate\n"
        "BLIP-2: 400 esempi | Florence2: 400 esempi | Idefics3: 100 esempi\n"
        f"Generato: {datetime.now().strftime('%d/%m/%Y %H:%M')}"
    )
    
    plt.figtext(0.02, 0.02, info_text, fontsize=9, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    # Migliora layout
    plt.tight_layout()
    
    # Salva il grafico
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"evaluation_results/baseline_bleu_complete_chart_{timestamp}.png"
    
    plt.savefig(filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ Grafico salvato: {filename}")
    
    # Mostra anche statistiche
    print("\n📊 STATISTICHE BLEU:")
    print("=" * 50)
    for model in models:
        scores = bleu_data[model]
        print(f"{model:>10}: BLEU-1={scores[0]:.4f} | BLEU-2={scores[1]:.4f} | BLEU-3={scores[2]:.4f} | BLEU-4={scores[3]:.4f}")
    
    print("\n🏆 MIGLIORI PER CATEGORIA:")
    print("=" * 50)
    for i, bleu_type in enumerate(['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4']):
        scores_for_metric = [(model, bleu_data[model][i]) for model in models]
        best_model, best_score = max(scores_for_metric, key=lambda x: x[1])
        print(f"{bleu_type}: {best_model} ({best_score:.4f})")
    
    return filename

if __name__ == "__main__":
    create_bleu_chart()
