#!/bin/bash
#SBATCH --job-name=baseline_idefics3_only
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=64GB
#SBATCH --gres=gpu:1
#SBATCH --time=02:00:00
#SBATCH --output=logs/baseline_idefics3_only_%j.out
#SBATCH --error=logs/baseline_idefics3_only_%j.err

echo "🔮 IDEFICS3 BASELINE ONLY - Dataset colori corretti"
echo "=================================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Start: $(date)"

source ~/.bashrc
eval "$(conda shell.bash hook)"
conda activate svg_env_new

export CUDA_VISIBLE_DEVICES=0
export TRANSFORMERS_CACHE=/tmp/hf_cache_$$
export HF_HOME=/tmp/hf_cache_$$
mkdir -p /tmp/hf_cache_$$

cd /work/tesi_ediluzio
mkdir -p evaluation_results/idefics3_only_colors_fixed

echo "🚀 Starting IDEFICS3 inference..."

python scripts/evaluation/evaluate_baseline_corrected.py \
    --test_file data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json \
    --output_dir evaluation_results/idefics3_only_colors_fixed \
    --models idefics3 \
    --max_examples 400 \
    --save_raw_predictions

echo "✅ IDEFICS3 completed: $(date)"
