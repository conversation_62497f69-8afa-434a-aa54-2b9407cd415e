#!/bin/bash
#SBATCH --job-name=test_llama_t9_4min
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32GB
#SBATCH --gres=gpu:1
#SBATCH --time=00:05:00
#SBATCH --output=logs/test_llama_t9_4min_%j.out
#SBATCH --error=logs/test_llama_t9_4min_%j.err

echo "🧪 TEST LLAMA T9 - 4 MINUTI"
echo "========================="
echo "Job ID: $SLURM_JOB_ID"
echo "Start: $(date)"

source ~/.bashrc
eval "$(conda shell.bash hook)"
conda activate svg_env_new

export CUDA_VISIBLE_DEVICES=0
export TRANSFORMERS_CACHE=/tmp/hf_cache_$$
export HF_HOME=/tmp/hf_cache_$$
mkdir -p /tmp/hf_cache_$$

cd /work/tesi_ediluzio

echo "🔍 Verifica checkpoint Llama T9..."
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t9_no_accumulation"
CHECKPOINTS=$(ls -1 "$OUTPUT_DIR" | grep "checkpoint-" | sort -V)
LATEST_CHECKPOINT=$(echo "$CHECKPOINTS" | tail -1)
echo "✅ Ultimo checkpoint: $LATEST_CHECKPOINT"

echo "🚀 Test caricamento modello e checkpoint..."
python -c "
import torch
print(f'CUDA available: {torch.cuda.is_available()}')
print(f'GPU count: {torch.cuda.device_count()}')
if torch.cuda.is_available():
    print(f'GPU name: {torch.cuda.get_device_name(0)}')
    print(f'GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')

# Test import
from transformers import AutoTokenizer, AutoModelForCausalLM
print('✅ Transformers import OK')

# Test tokenizer
tokenizer = AutoTokenizer.from_pretrained('meta-llama/Llama-3.1-8B-Instruct')
print('✅ Tokenizer caricato')

print('✅ Test completato con successo!')
"

echo "⏰ Sleep 3 minuti per simulare training..."
sleep 180

echo "✅ Test Llama T9 completato: $(date)"
