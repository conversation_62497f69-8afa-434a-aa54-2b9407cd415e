#!/bin/bash

echo "🎯 GENERAZIONE REPORT MODELLI TRAINED"
echo "====================================="

# Attiva ambiente
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Crea directory output
mkdir -p evaluation_results

# Genera report
echo "📊 Generazione report in corso..."
python generate_trained_models_report.py --output_dir evaluation_results/

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ REPORT GENERATO CON SUCCESSO!"
    echo "================================"
    echo "📁 File generati:"
    ls -la evaluation_results/trained_models_*
    echo ""
    echo "🎯 UTILIZZO:"
    echo "- Apri il file HTML nel browser per vedere il report completo"
    echo "- Il file PNG contiene il radar chart"
    echo "- Il file PDF è una versione vettoriale del radar chart"
else
    echo "❌ Errore nella generazione del report"
fi
