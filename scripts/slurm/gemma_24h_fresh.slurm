#!/bin/bash
#SBATCH --job-name=gemma_24h_fresh
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/gemma_24h_fresh_%j.out
#SBATCH --error=logs/gemma_24h_fresh_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

echo "🚀 GEMMA 24H FRESH START - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente
source ~/.bashrc
cd /work/tesi_ediluzio

# Verifica ambiente
echo "🔍 Verifica ambiente..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'✅ GPU count: {torch.cuda.device_count()}')"

# Parametri
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HF_HUB_DISABLE_TELEMETRY=1

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_24h_fresh_$(date +%Y%m%d_%H%M%S)"

echo "📂 Output directory: $OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

echo "✅ Avvio training GEMMA 24H FRESH..."

# Lancia training con torchrun per dual-GPU
python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29511 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path "google/gemma-2-2b-it" \
    --data_file "data/processed/xml_format_optimized/train_set_100k_final_90000.json" \
    --config_path "experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json" \
    --output_dir "$OUTPUT_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 GEMMA 24H FRESH COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort
else
    echo "❌ Output directory non trovata"
fi
