#!/bin/bash
#SBATCH --job-name=RESUME_llama_t9_deepspeed
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/RESUME_llama_t9_deepspeed_%j.out
#SBATCH --error=logs/RESUME_llama_t9_deepspeed_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 RESUME LLAMA T9 WITH DEEPSPEED - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 VERSIONI CORRETTE + DEEPSPEED"
echo "=================================="

# Fix librerie CUDA
echo "🔧 Fix librerie CUDA..."
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"

# VERSIONI CORRETTE: Transformers 4.45.0 + Accelerate 1.2.1 + PEFT >= 0.5.0 + DeepSpeed
echo "🔧 FORZA aggiornamento PEFT per risolvere errore versione..."
pip install "peft>=0.5.0" --upgrade --force-reinstall
pip install "deepspeed>=0.12.0" --upgrade
echo "🔧 Verifica altre versioni..."
python -c "import transformers; print(f'Transformers: {transformers.__version__}')" || pip install transformers==4.45.0
python -c "import accelerate; print(f'Accelerate: {accelerate.__version__}')" || pip install accelerate==1.2.1

echo "📋 Versioni installate:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import deepspeed; print(f'✅ DeepSpeed: {deepspeed.__version__}')"

echo "🔧 RIAPPLICO FIX PEFT dopo force-reinstall..."
sed -i 's/if len(active_adapters) > 1:/if hasattr(active_adapters, "__call__") and len(active_adapters()) > 1 or hasattr(active_adapters, "__len__") and len(active_adapters) > 1:/' /homes/ediluzio/.conda/envs/svg_env_new/lib/python3.10/site-packages/transformers/trainer.py
echo "✅ Fix PEFT riapplicato"

# Parametri per LLAMA T9
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t9_deepspeed_checkpoint_5500_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory e checkpoint
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t9_deepspeed"
CHECKPOINT_DIR="experiments/xml_direct_input/outputs/llama_t9_no_accumulation/checkpoint-5500"

# Crea directory output se non esiste
mkdir -p "$OUTPUT_DIR"

echo "📂 Resume LLAMA T9 da checkpoint: $CHECKPOINT_DIR"
echo "📂 Output in: $OUTPUT_DIR"

# Verifica checkpoint
if [ ! -d "$CHECKPOINT_DIR" ]; then
    echo "❌ ERRORE: Checkpoint non trovato: $CHECKPOINT_DIR"
    exit 1
fi

echo "✅ Checkpoint trovato, contenuto:"
ls -la "$CHECKPOINT_DIR"

# Copia checkpoint nella nuova directory
echo "🔄 Copio checkpoint nella directory DeepSpeed..."
cp -r "$CHECKPOINT_DIR"/* "$OUTPUT_DIR/"
echo "✅ Checkpoint copiato"

echo ""
echo "🚀 AVVIO LLAMA T9 CON DEEPSPEED DA CHECKPOINT-5500"
echo "================================================="

# Training LLAMA T9 con DeepSpeed
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t9_deepspeed.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 RESUME LLAMA T9 CON DEEPSPEED COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ LLAMA T9 DeepSpeed completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"/checkpoint-* | tail -3
else
    echo "❌ LLAMA T9 DeepSpeed fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
echo "📊 Checkpoint LLAMA T9 DeepSpeed disponibili:"
find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
