#!/bin/bash
#SBATCH --job-name=FINAL_gemma_t9_24h
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FINAL_gemma_t9_24h_%j.out
#SBATCH --error=logs/FINAL_gemma_t9_24h_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FINAL GEMMA T9 24H - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new che funziona
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri CORRETTI per distributed training (NO quantizzazione)
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_fixed_distributed_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export HF_HUB_DISABLE_TELEMETRY=1
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************
export DISABLE_4BIT_QUANTIZATION=true

# Output directory - TRAINING DA ZERO
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation"

echo "📂 Training DA ZERO in: $OUTPUT_DIR"

# Crea directory se non esiste
mkdir -p "$OUTPUT_DIR"

echo "✅ Directory pronta per training da zero"

echo "✅ Avvio FINAL GEMMA T9 24H..."

# Lancia training CORRETTO senza quantizzazione per distributed
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 FINAL GEMMA T9 24H COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory non trovata"
fi
