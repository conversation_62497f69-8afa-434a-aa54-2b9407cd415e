#!/bin/bash
#SBATCH --job-name=FINAL_gemma_t9_correct_versions
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FINAL_gemma_t9_correct_versions_%j.out
#SBATCH --error=logs/FINAL_gemma_t9_correct_versions_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FINAL GEMMA T9 CORRECT VERSIONS - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 INSTALLAZIONE VERSIONI CORRETTE COMPATIBILI"
echo "=============================================="

# Fix librerie CUDA
echo "🔧 Fix librerie CUDA..."
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"

# VERSIONI CORRETTE: Transformers 4.53.1 + Accelerate 1.2.1 (ultima senza keep_torch_compile)
echo "🔧 Installazione versioni CORRETTE COMPATIBILI..."
pip install --force-reinstall \
    transformers==4.53.1 \
    accelerate==1.2.1 \
    peft==0.4.0

echo "📋 Versioni CORRETTE installate:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Test import Trainer (il problema principale)
echo "🧪 Test import Trainer con versioni CORRETTE..."
python -c "
from transformers import Trainer
print('✅ Trainer import OK con versioni CORRETTE')
"

# Test unwrap_model senza keep_torch_compile
echo "🧪 Test unwrap_model senza keep_torch_compile..."
python -c "
from accelerate import Accelerator
import torch
accelerator = Accelerator()
model = torch.nn.Linear(10, 1)
try:
    unwrapped = accelerator.unwrap_model(model)
    print('✅ unwrap_model OK senza keep_torch_compile')
except Exception as e:
    print(f'❌ unwrap_model error: {e}')
"

# Parametri per GEMMA T9
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_correct_versions_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Disabilita operazioni problematiche
export TORCH_CUDA_ARCH_LIST="7.0;7.5;8.0;8.6"
export CUDA_VISIBLE_DEVICES="0"  # Single GPU per evitare problemi

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation"
mkdir -p "$OUTPUT_DIR"

echo "📂 Training GEMMA T9 con versioni CORRETTE in: $OUTPUT_DIR"
echo "✅ Avvio GEMMA T9 con Transformers 4.53.1 + Accelerate 1.2.1..."

# Training GEMMA T9 con versioni CORRETTE
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 FINAL GEMMA T9 CORRECT VERSIONS COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ GEMMA T9 Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ GEMMA T9 Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint GEMMA T9 disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory GEMMA T9 non trovata"
fi
