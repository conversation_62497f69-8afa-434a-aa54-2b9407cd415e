#!/bin/bash
#SBATCH --job-name=generate_final_report
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/generate_final_report_%j.out
#SBATCH --error=logs/generate_final_report_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=16G
#SBATCH --cpus-per-task=4
#SBATCH --qos=normal
#SBATCH --time=01:00:00

echo "🎯 GENERAZIONE REPORT FINALE MODELLI TRAINED - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 VERIFICA AMBIENTE"
echo "==================="
python -c "import matplotlib; print(f'✅ Matplotlib: {matplotlib.__version__}')"
python -c "import numpy; print(f'✅ NumPy: {numpy.__version__}')"
python -c "import json; print('✅ JSON: OK')"

echo ""
echo "📊 VERIFICA RISULTATI MODELLI TRAINED"
echo "====================================="

# Controlla se esistono i file di risultati
GEMMA_RESULTS="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/evaluation_results.json"
LLAMA_T8_RESULTS="experiments/xml_direct_input/outputs/llama_t8_24h/evaluation_results.json"
LLAMA_T9_RESULTS="experiments/xml_direct_input/outputs/llama_t9_no_accumulation/evaluation_results.json"

echo "🔍 Controllo file risultati:"
MODELS_READY=0

if [ -f "$GEMMA_RESULTS" ]; then
    echo "✅ Gemma T9: $GEMMA_RESULTS"
    MODELS_READY=$((MODELS_READY + 1))
else
    echo "⚠️ Gemma T9: File non trovato (training in corso?)"
fi

if [ -f "$LLAMA_T8_RESULTS" ]; then
    echo "✅ Llama T8: $LLAMA_T8_RESULTS"
    MODELS_READY=$((MODELS_READY + 1))
else
    echo "⚠️ Llama T8: File non trovato (training in corso?)"
fi

if [ -f "$LLAMA_T9_RESULTS" ]; then
    echo "✅ Llama T9: $LLAMA_T9_RESULTS"
    MODELS_READY=$((MODELS_READY + 1))
else
    echo "⚠️ Llama T9: File non trovato (training in corso?)"
fi

echo "📈 Modelli completati: $MODELS_READY/3"

echo ""
echo "📈 GENERAZIONE REPORT COMPLETO"
echo "=============================="

# Crea directory output se non esiste
mkdir -p evaluation_results

# Genera il report completo (HTML + Radar Chart)
python generate_trained_models_report.py --output_dir evaluation_results/

EXIT_CODE=$?

echo ""
echo "🏁 GENERAZIONE REPORT COMPLETATA"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo ""
    echo "✅ REPORT GENERATO CON SUCCESSO!"
    echo "================================"
    echo "📁 File generati in: evaluation_results/"
    ls -la evaluation_results/trained_models_*
    echo ""
    echo "🎯 PROSSIMI PASSI:"
    echo "- Controlla il file HTML per il report completo"
    echo "- Controlla il file PNG per il radar chart"
    echo "- I file sono pronti per la presentazione!"
    echo ""
    echo "📊 MODELLI COMPLETATI: $MODELS_READY/3"
    if [ $MODELS_READY -eq 3 ]; then
        echo "🎉 TUTTI I MODELLI COMPLETATI! Report finale pronto!"
    else
        echo "⏳ Alcuni modelli ancora in training. Report con placeholder generato."
    fi
else
    echo ""
    echo "❌ ERRORE NELLA GENERAZIONE DEL REPORT"
    echo "====================================="
    echo "Controlla i log per dettagli sull'errore"
fi

echo ""
echo "📊 RIEPILOGO FILE GENERATI:"
echo "=========================="
find evaluation_results/ -name "trained_models_*" -type f 2>/dev/null | sort
