#!/bin/bash
#SBATCH --job-name=test_resume_3min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/test_resume_3min_%j.out
#SBATCH --error=logs/test_resume_3min_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=00:05:00
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

echo "🧪 TEST RESUME 3 MINUTI - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente
source ~/.bashrc
cd /work/tesi_ediluzio

# Verifica ambiente
echo "🔍 Verifica ambiente..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HF_HUB_DISABLE_TELEMETRY=1

# Directory esistente da continuare (test con Gemma)
CONTINUE_DIR="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation"

echo "📂 Test resume da: $CONTINUE_DIR"

# Verifica che la directory esista
if [ ! -d "$CONTINUE_DIR" ]; then
    echo "❌ ERRORE: Directory non trovata: $CONTINUE_DIR"
    exit 1
fi

# Trova l'ultimo checkpoint
LAST_CHECKPOINT=$(find "$CONTINUE_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)

if [ -z "$LAST_CHECKPOINT" ]; then
    echo "❌ ERRORE: Nessun checkpoint trovato in $CONTINUE_DIR"
    exit 1
fi

echo "📂 Ultimo checkpoint trovato: $LAST_CHECKPOINT"

echo "✅ Avvio TEST RESUME 3 MINUTI..."

# Lancia training con timeout (3 minuti = 180 secondi)
timeout 180s python scripts/training/train_lora_resume_fixed.py \
    --model_name_or_path "google/gemma-2-2b-it" \
    --data_file "data/processed/xml_format_optimized/train_set_100k_final_90000.json" \
    --config_path "experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json" \
    --output_dir "$CONTINUE_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 TEST RESUME 3 MINUTI COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 124 ]; then
    echo "✅ Test terminato correttamente dopo 3 minuti (timeout)"
elif [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Test completato normalmente"
else
    echo "❌ Test fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$CONTINUE_DIR" ]; then
    echo "📊 Checkpoint disponibili dopo test:"
    find "$CONTINUE_DIR" -name "checkpoint-*" -type d | sort -V | tail -3
else
    echo "❌ Output directory non trovata"
fi
