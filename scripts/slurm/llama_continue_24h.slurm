#!/bin/bash
#SBATCH --job-name=llama_cont_24h
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/llama_cont_24h_%j.out
#SBATCH --error=logs/llama_cont_24h_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

echo "🚀 LLAMA CONTINUE 24H - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente
source ~/.bashrc
cd /work/tesi_ediluzio

# Verifica ambiente
echo "🔍 Verifica ambiente..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'✅ GPU count: {torch.cuda.device_count()}')"

# Parametri
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HF_HUB_DISABLE_TELEMETRY=1

# Directory esistente da continuare
CONTINUE_DIR="experiments/xml_direct_input/outputs/llama_t10_continue"

echo "📂 Continuando training da: $CONTINUE_DIR"

# Verifica che la directory esista
if [ ! -d "$CONTINUE_DIR" ]; then
    echo "❌ ERRORE: Directory non trovata: $CONTINUE_DIR"
    exit 1
fi

# Trova l'ultimo checkpoint
LAST_CHECKPOINT=$(find "$CONTINUE_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)

if [ -z "$LAST_CHECKPOINT" ]; then
    echo "❌ ERRORE: Nessun checkpoint trovato in $CONTINUE_DIR"
    exit 1
fi

echo "📂 Ultimo checkpoint trovato: $LAST_CHECKPOINT"

echo "✅ Avvio training LLAMA CONTINUE 24H..."

# Lancia training con torchrun per dual-GPU
python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29512 \
    scripts/training/train_lora_resume_fixed.py \
    --model_name_or_path "meta-llama/Llama-3.1-8B-Instruct" \
    --data_file "data/processed/xml_format_optimized/train_set_100k_final_90000.json" \
    --config_path "experiments/xml_direct_input/configs/llama_t9_no_accumulation.json" \
    --output_dir "$CONTINUE_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 LLAMA CONTINUE 24H COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $CONTINUE_DIR"
    ls -la "$CONTINUE_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$CONTINUE_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$CONTINUE_DIR" -name "checkpoint-*" -type d | sort
else
    echo "❌ Output directory non trovata"
fi
