#!/bin/bash
#SBATCH --job-name=TEST_gemma_t9_8min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_gemma_t9_8min_%j.out
#SBATCH --error=logs/TEST_gemma_t9_8min_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=00:10:00

echo "🧪 TEST GEMMA T9 8 MINUTI - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 FORZA aggiornamento PEFT per risolvere errore versione..."
pip install "peft>=0.5.0" --upgrade --force-reinstall
echo "🔧 Verifica altre versioni..."
python -c "import transformers; print(f'Transformers: {transformers.__version__}')" || pip install transformers==4.45.0
python -c "import accelerate; print(f'Accelerate: {accelerate.__version__}')" || pip install accelerate==1.2.1

echo "📋 Versioni installate:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"

echo "🔧 RIAPPLICO FIX PEFT dopo force-reinstall..."
sed -i 's/if len(active_adapters) > 1:/if hasattr(active_adapters, "__call__") and len(active_adapters()) > 1 or hasattr(active_adapters, "__len__") and len(active_adapters) > 1:/' /homes/ediluzio/.conda/envs/svg_env_new/lib/python3.10/site-packages/transformers/trainer.py
echo "✅ Fix PEFT riapplicato"

# Fix librerie CUDA
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"

# Parametri per TEST
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************
export CUDA_VISIBLE_DEVICES="0"

# Output directory per test
OUTPUT_DIR="experiments/xml_direct_input/outputs/test_gemma_t9_8min"
mkdir -p "$OUTPUT_DIR"

echo "📂 Test directory: $OUTPUT_DIR"

# Copia checkpoint-4000 per testare resume dal checkpoint più recente
echo "🔄 Preparazione test resume da checkpoint-4000..."
if [ -d "experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/checkpoint-4000" ]; then
    cp -r experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/checkpoint-4000 "$OUTPUT_DIR/"
    echo "✅ Checkpoint-4000 copiato per test resume"
else
    echo "⚠️ Checkpoint-4000 non trovato, test solo training nuovo"
fi

# Crea config test identica a quella originale ma con meno step
cat > "$OUTPUT_DIR/test_config.json" << EOF
{
    "model_name_or_path": "google/gemma-2-9b-it",
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 1,
    "max_steps": 20,
    "learning_rate": 5e-5,
    "save_steps": 10,
    "logging_steps": 1,
    "lora_r": 64,
    "lora_alpha": 128,
    "lora_dropout": 0.05,
    "lora_target_modules": [
        "q_proj", "k_proj", "v_proj", "o_proj", 
        "gate_proj", "up_proj", "down_proj"
    ],
    "load_in_4bit": true,
    "fp16": true,
    "gradient_checkpointing": true,
    "save_total_limit": 2
}
EOF

echo "✅ Config test creata (identica alla configurazione originale)"

echo ""
echo "🚀 AVVIO TEST 8 MINUTI - GEMMA T9 DA CHECKPOINT-4000"
echo "=================================================="

# Test training con timeout di 8 minuti (480 secondi)
timeout 480 python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path "$OUTPUT_DIR/test_config.json" \
    --output_dir "$OUTPUT_DIR" \
    --no_wandb

EXIT_CODE=$?

echo ""
echo "🏁 TEST 8 MINUTI COMPLETATO - GEMMA T9"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 124 ]; then
    echo "✅ TEST COMPLETATO: Timeout di 8 minuti raggiunto (normale)"
    echo "🎯 RESUME FUNZIONA: Training ripartito da checkpoint-4000"
elif [ $EXIT_CODE -eq 0 ]; then
    echo "✅ TEST COMPLETATO: Training finito prima del timeout"
    echo "🎯 RESUME FUNZIONA: Training completato con successo"
else
    echo "❌ TEST FALLITO: Errore durante il training (exit code: $EXIT_CODE)"
    echo "🔍 CONTROLLARE LOG PER DETTAGLI"
fi

# Verifica checkpoint creati
echo "📊 Checkpoint creati durante il test:"
find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V

# Verifica se ha fatto progressi
if [ -d "$OUTPUT_DIR/checkpoint-4010" ] || [ -d "$OUTPUT_DIR/checkpoint-4020" ]; then
    echo "🎉 SUCCESSO: Nuovi checkpoint creati, resume funziona!"
else
    echo "⚠️ ATTENZIONE: Nessun nuovo checkpoint, possibile problema"
fi

# Pulizia test
echo "🧹 Pulizia test..."
rm -rf "$OUTPUT_DIR"
echo "✅ Test directory rimossa"

echo "🎯 TEST GEMMA T9 8 MINUTI TERMINATO"
