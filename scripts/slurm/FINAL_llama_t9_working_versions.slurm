#!/bin/bash
#SBATCH --job-name=FINAL_llama_t9_working_versions
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FINAL_llama_t9_working_versions_%j.out
#SBATCH --error=logs/FINAL_llama_t9_working_versions_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FINAL LLAMA T9 WORKING VERSIONS - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 RIPRISTINO VERSIONI FUNZIONANTI"
echo "=================================="

# Ripristina versioni che funzionavano insieme (STESSA STRATEGIA VINCENTE)
echo "🔧 Installazione versioni FUNZIONANTI (Transformers 4.53.1 + Accelerate 0.34.0)..."
pip install --force-reinstall \
    transformers==4.53.1 \
    accelerate==0.34.0 \
    tokenizers==0.21.2

echo "📋 Versioni FUNZIONANTI installate:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Test import Trainer (era il problema)
echo "🧪 Test import Trainer..."
python -c "
from transformers import Trainer
print('✅ Trainer import OK')
"

# Parametri per training con versioni funzionanti
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t9_working_versions_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Workaround per il problema keep_torch_compile
export ACCELERATE_USE_FSDP=0
export ACCELERATE_USE_DEEPSPEED=0

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t9_no_accumulation"
mkdir -p "$OUTPUT_DIR"

echo "📂 Training con versioni FUNZIONANTI in: $OUTPUT_DIR"
echo "✅ Avvio LLAMA T9 con Transformers 4.53.1 + Accelerate 0.34.0..."

# Training SINGLE GPU per evitare problemi distributed
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 FINAL LLAMA T9 WORKING VERSIONS COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory non trovata"
fi
