#!/bin/bash
#SBATCH --job-name=test_llama_cont_3min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/test_llama_cont_3min_%j.out
#SBATCH --error=logs/test_llama_cont_3min_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=00:05:00
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

echo "🧪 TEST LLAMA CONTINUE 3 MINUTI - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Carica moduli
module load cuda/12.1

# Attiva ambiente
source ~/.bashrc
cd /work/tesi_ediluzio

# Verifica ambiente
echo "🔍 Verifica ambiente..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri TEST
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1

# Checkpoint da cui riprendere
RESUME_FROM="experiments/xml_direct_input/outputs/llama_t10_continue/checkpoint-12500"

echo "📂 Riprendendo da checkpoint: $RESUME_FROM"

# Verifica checkpoint
if [ ! -d "$RESUME_FROM" ]; then
    echo "❌ ERRORE: Checkpoint non trovato: $RESUME_FROM"
    exit 1
fi

echo "✅ Checkpoint trovato, avvio training TEST..."

# Lancia training TEST (3 minuti = 180 secondi)
timeout 180s python scripts/training/train_lora_resume_fixed.py \
    --model_name_or_path "meta-llama/Llama-3.1-8B-Instruct" \
    --data_file "data/processed/xml_format_optimized/train_set_100k_final_10000.json" \
    --config_path "experiments/xml_direct_input/configs/llama_t9_no_accumulation.json" \
    --output_dir "experiments/xml_direct_input/outputs/llama_continue_test_3min"

EXIT_CODE=$?

echo ""
echo "🏁 TEST LLAMA CONTINUE COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 124 ]; then
    echo "✅ Test terminato correttamente dopo 3 minuti (timeout)"
elif [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Test completato normalmente"
else
    echo "❌ Test fallito con codice: $EXIT_CODE"
fi

# Verifica output
if [ -d "experiments/xml_direct_input/outputs/llama_continue_test_3min" ]; then
    echo "📁 Output directory creata:"
    ls -la experiments/xml_direct_input/outputs/llama_continue_test_3min/
else
    echo "❌ Output directory non trovata"
fi
