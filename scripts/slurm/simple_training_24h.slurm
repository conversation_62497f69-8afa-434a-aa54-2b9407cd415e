#!/bin/bash
#SBATCH --job-name=simple_train_24h
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/simple_train_24h_%j.out
#SBATCH --error=logs/simple_train_24h_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=24:00:00
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

echo "🚀 SIMPLE TRAINING 24H - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente
source ~/.bashrc
cd /work/tesi_ediluzio

# Verifica ambiente
echo "🔍 Verifica ambiente..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HF_HUB_DISABLE_TELEMETRY=1

# Output directory con timestamp
OUTPUT_DIR="experiments/xml_direct_input/outputs/simple_train_24h_$(date +%Y%m%d_%H%M%S)"

echo "📂 Output directory: $OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

echo "✅ Avvio SIMPLE TRAINING 24H..."

# Lancia training semplice (usa lo script che funziona)
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path "google/gemma-2-2b-it" \
    --data_file "data/processed/xml_format_optimized/train_set_100k_final_90000.json" \
    --config_path "experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json" \
    --output_dir "$OUTPUT_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 SIMPLE TRAINING 24H COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V
else
    echo "❌ Output directory non trovata"
fi
