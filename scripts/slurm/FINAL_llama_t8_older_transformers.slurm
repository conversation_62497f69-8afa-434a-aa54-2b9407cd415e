#!/bin/bash
#SBATCH --job-name=FINAL_llama_t8_older_transformers
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FINAL_llama_t8_older_transformers_%j.out
#SBATCH --error=logs/FINAL_llama_t8_older_transformers_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FINAL LLAMA T8 OLDER TRANSFORMERS - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 INSTALLAZIONE VERSIONI SENZA keep_torch_compile"
echo "================================================="

# Fix librerie CUDA
echo "🔧 Fix librerie CUDA..."
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"

# VERSIONI SENZA keep_torch_compile: Transformers 4.45.0 + Accelerate 1.2.1
echo "🔧 Installazione Transformers 4.45.0 (SENZA keep_torch_compile)..."
pip install --force-reinstall \
    transformers==4.45.0 \
    accelerate==1.2.1 \
    peft==0.4.0

echo "📋 Versioni SENZA keep_torch_compile installate:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Test import Trainer
echo "🧪 Test import Trainer con Transformers 4.45.0..."
python -c "
from transformers import Trainer
print('✅ Trainer import OK con Transformers 4.45.0')
"

# Parametri per LLAMA T8
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_older_transformers_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Disabilita operazioni problematiche
export TORCH_CUDA_ARCH_LIST="7.0;7.5;8.0;8.6"
export CUDA_VISIBLE_DEVICES="0"  # Single GPU per evitare problemi

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"
mkdir -p "$OUTPUT_DIR"

echo "📂 Training LLAMA T8 con Transformers 4.45.0 in: $OUTPUT_DIR"
echo "✅ Avvio LLAMA T8 con Transformers 4.45.0 (SENZA keep_torch_compile)..."

# Training LLAMA T8 con Transformers VECCHIA
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 FINAL LLAMA T8 OLDER TRANSFORMERS COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ LLAMA T8 Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ LLAMA T8 Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint LLAMA T8 disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory LLAMA T8 non trovata"
fi
