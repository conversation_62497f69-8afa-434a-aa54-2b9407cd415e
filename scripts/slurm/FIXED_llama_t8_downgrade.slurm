#!/bin/bash
#SBATCH --job-name=FIXED_llama_t8_downgrade
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FIXED_llama_t8_downgrade_%j.out
#SBATCH --error=logs/FIXED_llama_t8_downgrade_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FIXED LLAMA T8 WITH TRANSFORMERS DOWNGRADE - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 DOWNGRADE TRANSFORMERS PER COMPATIBILITÀ"
echo "============================================"

# Verifica versioni attuali
echo "📋 Versioni PRIMA del downgrade:"
python -c "import transformers; print(f'Transformers: {transformers.__version__}')" 2>/dev/null || echo "Transformers: ERROR"
python -c "import accelerate; print(f'Accelerate: {accelerate.__version__}')" 2>/dev/null || echo "Accelerate: ERROR"
python -c "import peft; print(f'PEFT: {peft.__version__}')" 2>/dev/null || echo "PEFT: ERROR"

# Downgrade Transformers e tokenizers per compatibilità
echo "🔧 Downgrading Transformers to 4.44.2 and tokenizers to 0.19.1..."
pip install transformers==4.44.2 tokenizers==0.19.1 --force-reinstall

# Verifica versioni dopo downgrade
echo "📋 Versioni DOPO il downgrade:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri per distributed training
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_fixed_downgrade_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export HF_HUB_DISABLE_TELEMETRY=1
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"
mkdir -p "$OUTPUT_DIR"

echo "📂 Training in: $OUTPUT_DIR"
echo "✅ Avvio LLAMA T8 con Transformers downgrade..."

# Training con versione compatibile
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29501 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 FIXED LLAMA T8 DOWNGRADE COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory non trovata"
fi
