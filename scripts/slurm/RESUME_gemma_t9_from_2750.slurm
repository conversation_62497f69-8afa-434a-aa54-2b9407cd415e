#!/bin/bash
#SBATCH --job-name=RESUME_gemma_t9_from_2750
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/RESUME_gemma_t9_from_2750_%j.out
#SBATCH --error=logs/RESUME_gemma_t9_from_2750_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 RESUME GEMMA T9 FROM 2750 - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"

cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# VERSIONI CHE FUNZIONANO - NO UPGRADE!
echo "🔧 Mantengo versioni che funzionano..."
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"

# Parametri
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_resume_2750_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************
export CUDA_VISIBLE_DEVICES="0"

# TRUCCO: Rinomina checkpoint corrotto e usa 2750
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation"

echo "🔧 Backup checkpoint corrotto..."
if [ -d "$OUTPUT_DIR/checkpoint-3000" ]; then
    mv "$OUTPUT_DIR/checkpoint-3000" "$OUTPUT_DIR/checkpoint-3000.BACKUP"
    echo "✅ Checkpoint 3000 rinominato in BACKUP"
fi

echo "🚀 Training ripartirà da checkpoint-2750..."

# Training GEMMA T9 
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo "🏁 RESUME GEMMA T9 FROM 2750 COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ GEMMA T9 Resume da 2750 completato con successo"
else
    echo "❌ GEMMA T9 Resume da 2750 fallito"
    # Ripristina checkpoint originale se fallisce
    if [ -d "$OUTPUT_DIR/checkpoint-3000.BACKUP" ]; then
        mv "$OUTPUT_DIR/checkpoint-3000.BACKUP" "$OUTPUT_DIR/checkpoint-3000"
        echo "🔄 Checkpoint 3000 ripristinato"
    fi
fi
