#!/bin/bash
#SBATCH --job-name=TEST_download_llama_1min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_download_llama_1min_%j.out
#SBATCH --error=logs/TEST_download_llama_1min_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=00:05:00

echo "🧪 TEST DOWNLOAD LLAMA 1 MINUTO - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Parametri
export HUGGINGFACE_HUB_TOKEN=*************************************
export HF_HUB_DISABLE_TELEMETRY=1

echo "🔍 Test download modello Llama..."

# Test download del modello
python -c "
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

print('📥 Downloading Llama model...')
try:
    model = AutoModelForCausalLM.from_pretrained(
        'meta-llama/Llama-3.1-8B-Instruct',
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
    print('✅ Modello scaricato con successo!')
    
    tokenizer = AutoTokenizer.from_pretrained('meta-llama/Llama-3.1-8B-Instruct')
    print('✅ Tokenizer scaricato con successo!')
    
    print(f'📊 Modello: {model.config.model_type}')
    print(f'📊 Parametri: {model.num_parameters():,}')
    
except Exception as e:
    print(f'❌ Errore: {e}')
    import traceback
    traceback.print_exc()
"

echo ""
echo "🏁 TEST DOWNLOAD COMPLETATO"
echo "End time: $(date)"
