#!/bin/bash
#SBATCH --job-name=ORIGINAL_versions_exact
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/ORIGINAL_versions_exact_%j.out
#SBATCH --error=logs/ORIGINAL_versions_exact_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 ORIGINAL VERSIONS EXACT - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 RIPRISTINO VERSIONI ORIGINALI ESATTE"
echo "======================================="

# VERSIONI ESATTE dall'ambiente originale che funzionava (log 2600776)
echo "🔧 Installazione versioni ORIGINALI ESATTE..."
pip install --force-reinstall \
    transformers==4.53.1 \
    accelerate==0.26.0 \
    peft==0.4.0

echo "📋 Versioni ORIGINALI ESATTE installate:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Test import Trainer (il problema principale)
echo "🧪 Test import Trainer con versioni ORIGINALI..."
python -c "
from transformers import Trainer
print('✅ Trainer import OK con versioni ORIGINALI')
"

# Test distributed training
echo "🧪 Test distributed training..."
python -c "
import torch.distributed as dist
from accelerate import Accelerator
print('✅ Distributed imports OK')
"

# Parametri per training con versioni ORIGINALI
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=original_exact_gemma_t9_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation"
mkdir -p "$OUTPUT_DIR"

echo "📂 Training con versioni ORIGINALI ESATTE in: $OUTPUT_DIR"
echo "✅ Avvio GEMMA T9 con Transformers 4.53.1 + Accelerate 0.26.0 + PEFT 0.4.0..."

# Training con versioni ORIGINALI (single GPU per ora)
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE_GEMMA=$?

echo ""
echo "🔄 GEMMA T9 COMPLETATO (exit: $EXIT_CODE_GEMMA), ora LLAMA T8..."

# Se Gemma funziona, prova Llama T8
if [ $EXIT_CODE_GEMMA -eq 0 ]; then
    echo "✅ GEMMA OK! Avvio LLAMA T8..."
    
    export WANDB_RUN_NAME=original_exact_llama_t8_$(date +%Y%m%d_%H%M%S)
    OUTPUT_DIR_T8="experiments/xml_direct_input/outputs/llama_t8_24h"
    mkdir -p "$OUTPUT_DIR_T8"
    
    python scripts/training/train_lora_multi_gpu_simple.py \
        --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
        --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
        --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
        --output_dir "$OUTPUT_DIR_T8" \
        --use_wandb
    
    EXIT_CODE_T8=$?
    echo "🔄 LLAMA T8 COMPLETATO (exit: $EXIT_CODE_T8), ora LLAMA T9..."
    
    # Se anche T8 funziona, prova T9
    if [ $EXIT_CODE_T8 -eq 0 ]; then
        echo "✅ LLAMA T8 OK! Avvio LLAMA T9..."
        
        export WANDB_RUN_NAME=original_exact_llama_t9_$(date +%Y%m%d_%H%M%S)
        OUTPUT_DIR_T9="experiments/xml_direct_input/outputs/llama_t9_no_accumulation"
        mkdir -p "$OUTPUT_DIR_T9"
        
        python scripts/training/train_lora_multi_gpu_simple.py \
            --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
            --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
            --config_path experiments/xml_direct_input/configs/llama_t9_no_accumulation.json \
            --output_dir "$OUTPUT_DIR_T9" \
            --use_wandb
        
        EXIT_CODE_T9=$?
        echo "🔄 LLAMA T9 COMPLETATO (exit: $EXIT_CODE_T9)"
    else
        echo "❌ LLAMA T8 FALLITO, salto T9"
        EXIT_CODE_T9=999
    fi
else
    echo "❌ GEMMA FALLITO, salto T8 e T9"
    EXIT_CODE_T8=999
    EXIT_CODE_T9=999
fi

echo ""
echo "🏁 ORIGINAL VERSIONS EXACT COMPLETATO"
echo "Exit codes: GEMMA=$EXIT_CODE_GEMMA, T8=$EXIT_CODE_T8, T9=$EXIT_CODE_T9"
echo "End time: $(date)"

# Riassunto finale
if [ $EXIT_CODE_GEMMA -eq 0 ]; then
    echo "✅ GEMMA T9: SUCCESSO con versioni ORIGINALI"
else
    echo "❌ GEMMA T9: FALLITO"
fi

if [ $EXIT_CODE_T8 -eq 0 ]; then
    echo "✅ LLAMA T8: SUCCESSO con versioni ORIGINALI"
elif [ $EXIT_CODE_T8 -eq 999 ]; then
    echo "⏭️ LLAMA T8: SALTATO"
else
    echo "❌ LLAMA T8: FALLITO"
fi

if [ $EXIT_CODE_T9 -eq 0 ]; then
    echo "✅ LLAMA T9: SUCCESSO con versioni ORIGINALI"
elif [ $EXIT_CODE_T9 -eq 999 ]; then
    echo "⏭️ LLAMA T9: SALTATO"
else
    echo "❌ LLAMA T9: FALLITO"
fi
