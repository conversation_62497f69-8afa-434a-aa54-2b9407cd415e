#!/bin/bash
#SBATCH --job-name=florence2_hf_official
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32GB
#SBATCH --gres=gpu:1
#SBATCH --time=02:00:00
#SBATCH --output=logs/slurm/florence2_hf_official_%j.out
#SBATCH --error=logs/slurm/florence2_hf_official_%j.err

echo "🌸 FLORENCE2 BASELINE - DOCS HUGGINGFACE UFFICIALI"
echo "=================================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo ""

# Environment setup
source ~/.bashrc
eval "$(conda shell.bash hook)"
conda activate svg_env_new

# Check environment
echo "🔧 Environment check:"
python --version
pip list | grep -E "(torch|transformers|accelerate)"
nvidia-smi
echo ""

# Set environment variables
export CUDA_VISIBLE_DEVICES=0
export TRANSFORMERS_CACHE=/tmp/hf_cache_$$
export HF_HOME=/tmp/hf_cache_$$

# Create cache directory
mkdir -p /tmp/hf_cache_$$

# Change to project directory
cd /work/tesi_ediluzio

# Create output directory
mkdir -p experiments/baseline_results/florence2_hf_official

echo "🚀 Starting Florence2 inference..."
echo "Dataset: data/processed/xml_format_optimized/baseline_t7_corrected_400_with_images.json"
echo "Output: experiments/baseline_results/florence2_hf_official"
echo ""

# Run inference using the working script
python scripts/evaluation/evaluate_baseline_corrected.py \
    --test_file data/processed/xml_format_optimized/baseline_t7_corrected_400_with_images.json \
    --output_dir experiments/baseline_results/florence2_hf_official \
    --models florence2 \
    --max_examples 400 \
    --save_raw_predictions

echo ""
echo "✅ Florence2 inference completed!"
echo "End time: $(date)"
echo "Results in: experiments/baseline_results/florence2_hf_official/"
