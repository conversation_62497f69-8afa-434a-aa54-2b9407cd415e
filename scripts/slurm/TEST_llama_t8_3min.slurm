#!/bin/bash
#SBATCH --job-name=TEST_llama_t8_3min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_llama_t8_3min_%j.out
#SBATCH --error=logs/TEST_llama_t8_3min_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=00:05:00

echo "🧪 TEST LLAMA T8 3 MINUTI - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new che funziona
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri ESATTI come l'originale
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export HF_HUB_DISABLE_TELEMETRY=1
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory - CONTINUA da checkpoint esistente
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"

echo "📂 Test resume da: $OUTPUT_DIR"

# Verifica che la directory esista
if [ ! -d "$OUTPUT_DIR" ]; then
    echo "❌ ERRORE: Directory non trovata: $OUTPUT_DIR"
    exit 1
fi

# Trova l'ultimo checkpoint
LAST_CHECKPOINT=$(find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)

if [ -z "$LAST_CHECKPOINT" ]; then
    echo "❌ ERRORE: Nessun checkpoint trovato in $OUTPUT_DIR"
    exit 1
fi

echo "📂 Ultimo checkpoint trovato: $LAST_CHECKPOINT"

echo "✅ Avvio TEST LLAMA T8 3 MINUTI..."

# Lancia training con TORCHRUN come l'originale (3 minuti = 180 secondi)
timeout 180s torchrun --nproc_per_node=2 \
    --master_port=29501 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 TEST LLAMA T8 3 MINUTI COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 124 ]; then
    echo "✅ Test terminato correttamente dopo 3 minuti (timeout)"
elif [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Test completato normalmente"
else
    echo "❌ Test fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -3
else
    echo "❌ Output directory non trovata"
fi
