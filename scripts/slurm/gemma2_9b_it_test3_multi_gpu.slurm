#!/bin/bash
#SBATCH --job-name=gemma2_9b_it_test3_multi_gpu
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/gemma2_9b_it_test3_multi_gpu_%j.out
#SBATCH --error=logs/gemma2_9b_it_test3_multi_gpu_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 GEMMA 2 9B IT TEST3 MULTI-GPU OPTIMIZED"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente e compatibilità versioni
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Fix compatibilità Transformers/Accelerate
echo "🔧 Applicando fix compatibilità versioni..."
export TRANSFORMERS_VERBOSITY=error
export DISABLE_TORCH_COMPILE_CHECK=1

# Parametri ottimizzati per Gemma 2 9B
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma2_9b_it_test3_multi_gpu_optimized
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu"
mkdir -p "$OUTPUT_DIR"

echo "✅ Avvio GEMMA 2 9B IT MULTI-GPU OPTIMIZED..."

# Training con configurazione ottimizzata
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma2_9b_it_test3_multi_gpu_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma2_9b_it_test3_multi_gpu_optimized

echo "🏁 GEMMA 2 9B IT TRAINING COMPLETATO"
echo "End time: $(date)"
echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
