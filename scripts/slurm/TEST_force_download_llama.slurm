#!/bin/bash
#SBATCH --job-name=TEST_force_download_llama
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_force_download_llama_%j.out
#SBATCH --error=logs/TEST_force_download_llama_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=00:10:00

echo "🧪 TEST FORCE DOWNLOAD LLAMA - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Parametri
export HUGGINGFACE_HUB_TOKEN=*************************************
export HF_HUB_DISABLE_TELEMETRY=1

echo "🔍 Force download modello Llama con force_download=True..."

# Test download forzato del modello
python -c "
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

print('📥 Force downloading Llama model...')
try:
    # Prima prova con force_download
    model = AutoModelForCausalLM.from_pretrained(
        'meta-llama/Llama-3.1-8B-Instruct',
        torch_dtype=torch.float16,
        trust_remote_code=True,
        force_download=True,
        resume_download=False
    )
    print('✅ Modello scaricato con successo!')
    
    tokenizer = AutoTokenizer.from_pretrained(
        'meta-llama/Llama-3.1-8B-Instruct',
        force_download=True
    )
    print('✅ Tokenizer scaricato con successo!')
    
    print(f'📊 Modello: {model.config.model_type}')
    print(f'📊 Parametri: {model.num_parameters():,}')
    
except Exception as e:
    print(f'❌ Errore con force_download: {e}')
    
    # Prova alternativa: usa local_files_only=False
    print('🔄 Tentativo alternativo senza cache...')
    try:
        model = AutoModelForCausalLM.from_pretrained(
            'meta-llama/Llama-3.1-8B-Instruct',
            torch_dtype=torch.float16,
            trust_remote_code=True,
            local_files_only=False,
            use_cache=False
        )
        print('✅ Modello scaricato con approccio alternativo!')
    except Exception as e2:
        print(f'❌ Errore anche con approccio alternativo: {e2}')
        import traceback
        traceback.print_exc()
"

echo ""
echo "🏁 TEST FORCE DOWNLOAD COMPLETATO"
echo "End time: $(date)"
