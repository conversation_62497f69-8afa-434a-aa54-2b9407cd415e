#!/bin/bash
#SBATCH --job-name=TEST_llama_alt_3min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_llama_alt_3min_%j.out
#SBATCH --error=logs/TEST_llama_alt_3min_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=00:05:00

echo "🧪 TEST LLAMA ALTERNATIVO 3 MINUTI - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new che funziona
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri ESATTI come l'originale
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export HF_HUB_DISABLE_TELEMETRY=1
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************

# Test con modello Llama alternativo (non corrotto)
echo "🔍 Test con modello Llama alternativo..."

# Prova con meta-llama/Llama-2-7b-chat-hf (più piccolo, dovrebbe funzionare)
python -c "
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

print('📥 Testing alternative Llama model...')
try:
    model = AutoModelForCausalLM.from_pretrained(
        'meta-llama/Llama-2-7b-chat-hf',
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
    print('✅ Modello alternativo scaricato con successo!')
    
    tokenizer = AutoTokenizer.from_pretrained('meta-llama/Llama-2-7b-chat-hf')
    print('✅ Tokenizer alternativo scaricato con successo!')
    
    print(f'📊 Modello: {model.config.model_type}')
    print(f'📊 Parametri: {model.num_parameters():,}')
    
except Exception as e:
    print(f'❌ Errore con modello alternativo: {e}')
    
    # Prova con un modello ancora più semplice
    print('🔄 Tentativo con modello più semplice...')
    try:
        model = AutoModelForCausalLM.from_pretrained(
            'microsoft/DialoGPT-medium',
            torch_dtype=torch.float16
        )
        print('✅ Modello semplice funziona!')
    except Exception as e2:
        print(f'❌ Errore anche con modello semplice: {e2}')
        import traceback
        traceback.print_exc()
"

echo ""
echo "🏁 TEST LLAMA ALTERNATIVO COMPLETATO"
echo "End time: $(date)"
