#!/bin/bash
#SBATCH --job-name=baseline_all_models
#SBATCH --output=logs/blip2_corrected_%j.out
#SBATCH --error=logs/blip2_corrected_%j.err
#SBATCH --time=02:00:00
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=32G

echo "🤖 BLIP-2 BASELINE CORRECTED - Job $SLURM_JOB_ID"
echo "📅 Started at: $(date)"

# Attiva ambiente
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Vai alla directory di lavoro
cd /work/tesi_ediluzio

echo "🔧 Fixing BLIP-2 prompt and generation parameters..."

# Esegui BLIP-2 con parametri corretti per SVG
python3 scripts/evaluation/evaluate_baseline_corrected.py \
    --test_file data/processed/xml_format_optimized/baseline_t7_corrected_400_with_images.json \
    --output_dir evaluation_results/baseline_all_models \
    --models blip2 florence2 idefics3 \
    --max_examples 400

echo "✅ BLIP-2 Corrected completed at: $(date)"
