#!/bin/bash
#SBATCH --job-name=RESUME_llama_t8_from_checkpoint
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/RESUME_llama_t8_from_checkpoint_%j.out
#SBATCH --error=logs/RESUME_llama_t8_from_checkpoint_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 RESUME LLAMA T8 FROM CHECKPOINT - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 INSTALLAZIONE VERSIONI CORRETTE"
echo "=================================="

# Fix librerie CUDA
echo "🔧 Fix librerie CUDA..."
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"

# VERSIONI CORRETTE: Transformers 4.45.0 + Accelerate 1.2.1 + PEFT >= 0.5.0
echo "🔧 FORZA aggiornamento PEFT per risolvere errore versione..."
pip install "peft>=0.5.0" --upgrade --force-reinstall
echo "🔧 Verifica altre versioni..."
python -c "import transformers; print(f'Transformers: {transformers.__version__}')" || pip install transformers==4.45.0
python -c "import accelerate; print(f'Accelerate: {accelerate.__version__}')" || pip install accelerate==1.2.1

echo "📋 Versioni installate:"
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"

echo "🔧 RIAPPLICO FIX PEFT dopo force-reinstall..."
sed -i 's/if len(active_adapters) > 1:/if hasattr(active_adapters, "__call__") and len(active_adapters()) > 1 or hasattr(active_adapters, "__len__") and len(active_adapters) > 1:/' /homes/ediluzio/.conda/envs/svg_env_new/lib/python3.10/site-packages/transformers/trainer.py
echo "✅ Fix PEFT riapplicato"

# Parametri per LLAMA T8
export WANDB_DISABLED=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_resume_checkpoint_4750_$(date +%Y%m%d_%H%M%S)
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Disabilita operazioni problematiche
export TORCH_CUDA_ARCH_LIST="7.0;7.5;8.0;8.6"
export CUDA_VISIBLE_DEVICES="0"  # Single GPU per evitare problemi

# Output directory e checkpoint
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"
CHECKPOINT_DIR="$OUTPUT_DIR/checkpoint-4500"

echo "📂 Resume LLAMA T8 da checkpoint: $CHECKPOINT_DIR (4500 - 4750 era corrotto)"

# Verifica checkpoint
if [ ! -d "$CHECKPOINT_DIR" ]; then
    echo "❌ ERRORE: Checkpoint non trovato: $CHECKPOINT_DIR"
    exit 1
fi

echo "✅ Checkpoint trovato, contenuto:"
ls -la "$CHECKPOINT_DIR"

echo ""
echo "🚀 AVVIO LLAMA T8 RESUME DA CHECKPOINT-3500"
echo "=========================================="

# Training LLAMA T8 con resume AUTOMATICO (script trova ultimo checkpoint)
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 RESUME LLAMA T8 COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ LLAMA T8 Resume completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"/checkpoint-* | tail -3
else
    echo "❌ LLAMA T8 Resume fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
echo "📊 Checkpoint LLAMA T8 disponibili:"
find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
