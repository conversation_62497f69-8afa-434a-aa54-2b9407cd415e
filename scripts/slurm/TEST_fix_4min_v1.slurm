#!/bin/bash
#SBATCH --job-name=TEST_fix_4min_v1
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_fix_4min_v1_%j.out
#SBATCH --error=logs/TEST_fix_4min_v1_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=00:05:00

echo "🧪 TEST FIX PEFT 4 MINUTI - VERSIONE 1 - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "🔧 VERIFICA VERSIONI (NO reinstall per mantenere fix PEFT)"
echo "======================================================="

# Fix librerie CUDA
export LD_LIBRARY_PATH="/usr/local/cuda/lib64:$LD_LIBRARY_PATH"
export CUDA_HOME="/usr/local/cuda"

# Verifica versioni senza reinstallare
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"

# Verifica fix PEFT
echo "🔍 Verifica fix PEFT applicato:"
sed -n '2713p' /homes/ediluzio/.conda/envs/svg_env_new/lib/python3.10/site-packages/transformers/trainer.py

# Parametri per TEST
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************
export CUDA_VISIBLE_DEVICES="0"

# Output directory per test
OUTPUT_DIR="experiments/xml_direct_input/outputs/test_fix_v1"
mkdir -p "$OUTPUT_DIR"

echo "📂 Test directory: $OUTPUT_DIR"

# Crea config test minimale
cat > "$OUTPUT_DIR/test_config.json" << EOF
{
    "model_name_or_path": "google/gemma-2-9b-it",
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 1,
    "max_steps": 10,
    "learning_rate": 5e-5,
    "save_steps": 5,
    "logging_steps": 1,
    "lora_r": 16,
    "lora_alpha": 32,
    "lora_dropout": 0.05,
    "load_in_4bit": true,
    "fp16": true,
    "gradient_checkpointing": true
}
EOF

echo "✅ Config test creata"

echo ""
echo "🚀 AVVIO TEST 4 MINUTI - VERSIONE 1"
echo "==================================="

# Test training con timeout di 4 minuti
timeout 240 python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path "$OUTPUT_DIR/test_config.json" \
    --output_dir "$OUTPUT_DIR" \
    --no_wandb

EXIT_CODE=$?

echo ""
echo "🏁 TEST 4 MINUTI COMPLETATO - VERSIONE 1"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 124 ]; then
    echo "✅ TEST COMPLETATO: Timeout di 4 minuti raggiunto (normale)"
elif [ $EXIT_CODE -eq 0 ]; then
    echo "✅ TEST COMPLETATO: Training finito prima del timeout"
else
    echo "❌ TEST FALLITO: Errore durante il training (exit code: $EXIT_CODE)"
fi

# Verifica checkpoint creati
echo "📊 Checkpoint creati durante il test:"
find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V

# Pulizia test
echo "🧹 Pulizia test..."
rm -rf "$OUTPUT_DIR"
echo "✅ Test directory rimossa"

echo "🎯 TEST VERSIONE 1 TERMINATO"
