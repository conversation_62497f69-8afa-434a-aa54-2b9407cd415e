#!/bin/bash
#SBATCH --job-name=test_python_path_3min
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/test_python_path_3min_%j.out
#SBATCH --error=logs/test_python_path_3min_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=00:05:00

echo "🧪 TEST PYTHON PATH 3 MINUTI - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Forza ambiente base
cd /work/tesi_ediluzio
export PATH="/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin:$PATH"
export PYTHONPATH=""

# Verifica ambiente
echo "🔍 Verifica ambiente base forzato..."
echo "Python path: $(which python)"
/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HF_HUB_DISABLE_TELEMETRY=1

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/test_python_path_3min"

echo "📂 Output directory: $OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

echo "✅ Avvio TEST PYTHON PATH 3 MINUTI..."

# Lancia training con timeout (3 minuti = 180 secondi)
timeout 180s /homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path "google/gemma-2-2b-it" \
    --data_file "data/processed/xml_format_optimized/train_set_100k_final_90000.json" \
    --config_path "experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json" \
    --output_dir "$OUTPUT_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 TEST PYTHON PATH 3 MINUTI COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 124 ]; then
    echo "✅ Test terminato correttamente dopo 3 minuti (timeout)"
elif [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Test completato normalmente"
else
    echo "❌ Test fallito con codice: $EXIT_CODE"
fi

# Verifica output
if [ -d "$OUTPUT_DIR" ]; then
    echo "📁 Output directory creata:"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Output directory non trovata"
fi
