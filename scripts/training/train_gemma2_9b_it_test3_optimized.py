#!/usr/bin/env python3
"""
Training script SPECIFICO per GEMMA2_9B_IT_TEST3_MULTI_GPU_OPTIMIZED
Score WandB: 0.3019

Ottimizzazioni specifiche:
- Flash Attention 2.0
- Soft capping (attention + output)
- Sliding window attention
- LoRA rank 64, alpha 128
- Gradient accumulation 16
- Learning rate 1e-4
"""

import os
import sys
import json
import logging
import argparse
import torch
import torch.distributed as dist
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
import wandb

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_distributed():
    """Setup distributed training"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
        
        dist.init_process_group(backend='nccl')
        torch.cuda.set_device(local_rank)
        
        logger.info(f"🔧 Distributed setup: Rank {rank}/{world_size}, Local rank: {local_rank}")
        return rank, world_size, local_rank
    else:
        logger.info("🔧 Single GPU training")
        return 0, 1, 0

def load_config(config_path):
    """Load training configuration"""
    with open(config_path, 'r') as f:
        config = json.load(f)
    logger.info(f"Configurazione caricata da {config_path}")
    return config

def setup_model_and_tokenizer(config, disable_quantization=False):
    """Setup model with Gemma 2 specific optimizations"""
    model_name = config["model_name_or_path"]
    
    # Tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Model configuration with Gemma 2 optimizations
    model_kwargs = {
        "torch_dtype": torch.float16,
        "device_map": None,  # Handled by distributed training
        "trust_remote_code": True,
    }
    
    # Gemma 2 specific optimizations for score 0.3019
    if "gemma-2" in model_name.lower():
        logger.info("🔧 Applicando ottimizzazioni Gemma 2 per score 0.3019...")
        model_kwargs.update({
            "use_flash_attention_2": True,
            "attn_implementation": "flash_attention_2",
            # Soft capping parameters
            "attention_softmax_in_fp32": True,
            # Sliding window attention
            "sliding_window": 4096,
        })
    
    # Quantization (disabled for distributed training)
    if config.get("load_in_4bit", False) and not disable_quantization:
        logger.info("🔧 Quantizzazione 4-bit attivata")
        from transformers import BitsAndBytesConfig
        bnb_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
        )
        model_kwargs['quantization_config'] = bnb_config
    elif disable_quantization:
        logger.info("🔧 Quantizzazione disabilitata per distributed training")
    
    # Load model
    model = AutoModelForCausalLM.from_pretrained(model_name, **model_kwargs)
    
    # Enable gradient checkpointing
    if config.get("gradient_checkpointing", True):
        model.gradient_checkpointing_enable()
        logger.info("✅ Gradient checkpointing abilitato")
    
    return model, tokenizer

def setup_lora(model, config):
    """Setup LoRA with configuration for score 0.3019"""
    lora_config = LoraConfig(
        r=config.get("lora_r", 64),  # Rank 64 per score 0.3019
        lora_alpha=config.get("lora_alpha", 128),  # Alpha 128 per score 0.3019
        target_modules=config.get("lora_target_modules", [
            "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj"
        ]),
        lora_dropout=config.get("lora_dropout", 0.1),
        bias=config.get("lora_bias", "none"),
        task_type=TaskType.CAUSAL_LM,
    )
    
    model = get_peft_model(model, lora_config)
    
    # Print trainable parameters
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Parametri addestrabili: {trainable_params}")
    logger.info(f"Tutti i parametri: {total_params}")
    logger.info(f"Percentuale parametri addestrabili: {100 * trainable_params / total_params:.4f}%")
    
    return model

def load_dataset(config):
    """Load and prepare dataset"""
    # Load training data
    with open(config["data_file"], 'r') as f:
        train_data = json.load(f)
    
    # Load validation data
    with open(config["val_file"], 'r') as f:
        val_data = json.load(f)
    
    # Convert to datasets
    train_dataset = Dataset.from_list(train_data)
    val_dataset = Dataset.from_list(val_data)
    
    logger.info(f"Dataset di training: {len(train_dataset)} esempi")
    logger.info(f"Dataset di validazione: {len(val_dataset)} esempi")
    
    return train_dataset, val_dataset

def tokenize_function(examples, tokenizer, max_length):
    """Tokenize examples"""
    inputs = examples["input"]
    targets = examples["output"]
    
    # Combine input and target
    full_texts = [f"{inp} {tgt}" for inp, tgt in zip(inputs, targets)]
    
    # Tokenize
    tokenized = tokenizer(
        full_texts,
        truncation=True,
        padding=False,
        max_length=max_length,
        return_tensors=None
    )
    
    # Labels are the same as input_ids for causal LM
    tokenized["labels"] = tokenized["input_ids"].copy()
    
    return tokenized

def main():
    parser = argparse.ArgumentParser(description="Training Gemma2 9B IT Test3 Multi-GPU Optimized")
    parser.add_argument("--model_name_or_path", type=str, required=True)
    parser.add_argument("--data_file", type=str, required=True)
    parser.add_argument("--config_path", type=str, required=True)
    parser.add_argument("--output_dir", type=str, required=True)
    parser.add_argument("--disable_quantization", action="store_true")
    parser.add_argument("--use_wandb", action="store_true")
    parser.add_argument("--wandb_project", type=str, default="svg_captioning")
    parser.add_argument("--wandb_run_name", type=str, default="gemma2_9b_it_test3_multi_gpu_optimized")
    
    args = parser.parse_args()
    
    # Setup distributed training
    rank, world_size, local_rank = setup_distributed()
    
    # Load configuration
    config = load_config(args.config_path)
    
    # Setup model and tokenizer
    model, tokenizer = setup_model_and_tokenizer(config, args.disable_quantization)
    
    # Setup LoRA
    model = setup_lora(model, config)
    
    # Load dataset
    train_dataset, val_dataset = load_dataset(config)
    
    # Tokenize datasets
    max_length = config.get("max_length", 2048)
    train_dataset = train_dataset.map(
        lambda x: tokenize_function(x, tokenizer, max_length),
        batched=True,
        remove_columns=train_dataset.column_names
    )
    val_dataset = val_dataset.map(
        lambda x: tokenize_function(x, tokenizer, max_length),
        batched=True,
        remove_columns=val_dataset.column_names
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Training arguments optimized for score 0.3019
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        per_device_train_batch_size=config.get("per_device_train_batch_size", 1),
        per_device_eval_batch_size=config.get("per_device_eval_batch_size", 1),
        gradient_accumulation_steps=config.get("gradient_accumulation_steps", 16),  # 16 per score 0.3019
        learning_rate=config.get("learning_rate", 1e-4),  # 1e-4 per score 0.3019
        lr_scheduler_type=config.get("lr_scheduler_type", "cosine_with_restarts"),
        warmup_ratio=config.get("warmup_ratio", 0.05),
        weight_decay=config.get("weight_decay", 0.01),
        max_steps=config.get("max_steps", 45000),
        eval_steps=config.get("eval_steps", 500),
        save_steps=config.get("save_steps", 250),
        logging_steps=config.get("logging_steps", 50),
        evaluation_strategy="steps",
        save_strategy="steps",
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        save_total_limit=5,
        fp16=config.get("fp16", True),
        gradient_checkpointing=config.get("gradient_checkpointing", True),
        dataloader_num_workers=config.get("dataloader_num_workers", 4),
        remove_unused_columns=False,
        report_to=["wandb"] if args.use_wandb else [],
        run_name=args.wandb_run_name if args.use_wandb else None,
        ddp_find_unused_parameters=False,
        optim=config.get("optim", "adamw_torch_fused"),
        max_grad_norm=config.get("max_grad_norm", 0.5),
    )
    
    # Initialize wandb
    if args.use_wandb and rank == 0:
        wandb.init(
            project=args.wandb_project,
            name=args.wandb_run_name,
            config=config
        )
        logger.info(f"✅ WandB inizializzato: {args.wandb_project}/{args.wandb_run_name}")
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )
    
    # Start training
    logger.info("🚀 Avvio training Gemma2 9B IT Test3 Multi-GPU Optimized...")
    trainer.train()
    
    # Save final model
    if rank == 0:
        trainer.save_model()
        logger.info(f"✅ Modello salvato in {args.output_dir}")
    
    logger.info("🏁 Training completato!")

if __name__ == "__main__":
    main()
