<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Baseline Models - Complete BLEU Analysis Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 25px;
            font-size: 1.8em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .model-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #3498db;
        }
        .model-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .bleu-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .bleu-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        .bleu-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        .bleu-table tr:hover {
            background: #f8f9fa;
        }
        .metric-value {
            font-weight: bold;
            color: #2c3e50;
        }
        .bleu-1 { color: #e74c3c; }
        .bleu-2 { color: #f39c12; }
        .bleu-3 { color: #f1c40f; }
        .bleu-4 { color: #27ae60; }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .comparison-table th {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 18px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1em;
        }
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 1.05em;
        }
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .comparison-table tr:hover {
            background: #e3f2fd;
        }
        .best-score {
            background: #d4edda !important;
            font-weight: bold;
            color: #155724;
        }
        .model-name {
            font-weight: bold;
            color: #2c3e50;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .radar-section {
            text-align: center;
            margin: 30px 0;
        }
        .radar-section img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Baseline Models - Complete BLEU Analysis</h1>
            <p>Analisi Completa BLEU-1, BLEU-2, BLEU-3, BLEU-4 per Modelli Baseline</p>
            <p>🗓️ Report generato: 11 Luglio 2025</p>
        </div>
        
        <div class="content">
            
            <div class="section">
                <h2>📈 Confronto BLEU Completo (1-4)</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Modello</th>
                            <th>Esempi</th>
                            <th>BLEU-1</th>
                            <th>BLEU-2</th>
                            <th>BLEU-3</th>
                            <th>BLEU-4</th>
                            <th>METEOR</th>
                            <th>CIDEr</th>
                            <th>CLIPScore</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="model-name">🤖 BLIP-2</td>
                            <td>400</td>
                            <td class="best-score">0.2381</td>
                            <td class="best-score">0.1322</td>
                            <td>0.0757</td>
                            <td>0.0506</td>
                            <td>0.2227</td>
                            <td>0.4428</td>
                            <td>32.54</td>
                        </tr>
                        <tr>
                            <td class="model-name">🎨 Florence2</td>
                            <td>400</td>
                            <td class="best-score">0.2507</td>
                            <td class="best-score">0.1514</td>
                            <td>0.0675</td>
                            <td>0.0396</td>
                            <td class="best-score">0.4150</td>
                            <td>0.4449</td>
                            <td class="best-score">34.20</td>
                        </tr>
                        <tr>
                            <td class="model-name">🔍 Idefics3</td>
                            <td>100</td>
                            <td>0.1269</td>
                            <td>0.1066</td>
                            <td class="best-score">0.0933</td>
                            <td class="best-score">0.0789</td>
                            <td>0.3836</td>
                            <td class="best-score">1.5045</td>
                            <td>33.09</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h2>🎯 Analisi Dettagliata BLEU per Modello</h2>
                <div class="metrics-grid">
                    
                    <div class="model-card">
                        <h3>🤖 BLIP-2 (400 esempi)</h3>
                        <table class="bleu-table">
                            <tr>
                                <td><strong>BLEU-1</strong></td>
                                <td class="metric-value bleu-1">0.2381</td>
                                <td>🥇 Migliore unigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-2</strong></td>
                                <td class="metric-value bleu-2">0.1322</td>
                                <td>🥇 Migliore bigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-3</strong></td>
                                <td class="metric-value bleu-3">0.0757</td>
                                <td>🥈 Secondo trigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-4</strong></td>
                                <td class="metric-value bleu-4">0.0506</td>
                                <td>🥈 Secondo 4-gram</td>
                            </tr>
                        </table>
                        <p><strong>Punti di forza:</strong> Eccellente per n-gram corti (1-2), buona fluidità generale</p>
                    </div>

                    <div class="model-card">
                        <h3>🎨 Florence2 (400 esempi)</h3>
                        <table class="bleu-table">
                            <tr>
                                <td><strong>BLEU-1</strong></td>
                                <td class="metric-value bleu-1">0.2507</td>
                                <td>🥇 Migliore unigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-2</strong></td>
                                <td class="metric-value bleu-2">0.1514</td>
                                <td>🥇 Migliore bigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-3</strong></td>
                                <td class="metric-value bleu-3">0.0675</td>
                                <td>🥉 Terzo trigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-4</strong></td>
                                <td class="metric-value bleu-4">0.0396</td>
                                <td>🥉 Terzo 4-gram</td>
                            </tr>
                        </table>
                        <p><strong>Punti di forza:</strong> Migliore METEOR e CLIPScore, ottimo per descrizioni semantiche</p>
                    </div>

                    <div class="model-card">
                        <h3>🔍 Idefics3 (100 esempi)</h3>
                        <table class="bleu-table">
                            <tr>
                                <td><strong>BLEU-1</strong></td>
                                <td class="metric-value bleu-1">0.1269</td>
                                <td>🥉 Terzo unigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-2</strong></td>
                                <td class="metric-value bleu-2">0.1066</td>
                                <td>🥉 Terzo bigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-3</strong></td>
                                <td class="metric-value bleu-3">0.0933</td>
                                <td>🥇 Migliore trigram</td>
                            </tr>
                            <tr>
                                <td><strong>BLEU-4</strong></td>
                                <td class="metric-value bleu-4">0.0789</td>
                                <td>🥇 Migliore 4-gram</td>
                            </tr>
                        </table>
                        <p><strong>Punti di forza:</strong> Eccellente per n-gram lunghi (3-4), migliore CIDEr, descrizioni precise</p>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📊 Statistiche Riassuntive</h2>
                <div class="summary-stats">
                    <div class="stat-card">
                        <div class="stat-value">3</div>
                        <div class="stat-label">Modelli Baseline</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">900</div>
                        <div class="stat-label">Esempi Totali</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">100%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">7</div>
                        <div class="stat-label">Metriche Valutate</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🎯 Conclusioni BLEU Analysis</h2>
                <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; border-left: 5px solid #3498db;">
                    <h3>🏆 Ranking per Categoria BLEU:</h3>
                    <ul style="font-size: 1.1em; line-height: 1.8;">
                        <li><strong>BLEU-1 & BLEU-2:</strong> 🥇 Florence2 > 🥈 BLIP-2 > 🥉 Idefics3</li>
                        <li><strong>BLEU-3 & BLEU-4:</strong> 🥇 Idefics3 > 🥈 BLIP-2 > 🥉 Florence2</li>
                        <li><strong>Semantica (METEOR):</strong> 🥇 Florence2 > 🥈 Idefics3 > 🥉 BLIP-2</li>
                        <li><strong>Diversità (CIDEr):</strong> 🥇 Idefics3 > 🥈 Florence2 ≈ BLIP-2</li>
                        <li><strong>Similarità Visiva (CLIP):</strong> 🥇 Florence2 > 🥈 Idefics3 > 🥉 BLIP-2</li>
                    </ul>
                    
                    <h3>💡 Insights Chiave:</h3>
                    <ul style="font-size: 1.1em; line-height: 1.8;">
                        <li><strong>Florence2:</strong> Migliore per fluidità generale e comprensione semantica</li>
                        <li><strong>Idefics3:</strong> Eccellente per precisione terminologica e diversità</li>
                        <li><strong>BLIP-2:</strong> Buon compromesso generale, stabile su tutte le metriche</li>
                    </ul>
                </div>
            </div>

            <div class="section">
                <h2>📈 Radar Chart Comparativo</h2>
                <div class="radar-section">
                    <img src="baseline_models_comparative_radar_20250704_190422.png" alt="Radar Chart Baseline Models">
                    <p><em>Confronto visivo delle performance tra i 3 modelli baseline</em></p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🎯 Report generato automaticamente dal sistema di evaluation</p>
            <p>Dataset: SVG-to-Caption con immagini colorate | Versioni: Transformers 4.45.0 + PEFT 0.16.0</p>
            <p>📅 Data: 11 Luglio 2025 | 🔬 Metriche: BLEU-1/2/3/4, METEOR, CIDEr, CLIPScore</p>
        </div>
    </div>
</body>
</html>
