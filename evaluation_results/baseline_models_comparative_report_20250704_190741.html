
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confronto Modelli Baseline - Report Qualitativo</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; font-size: 2.5em; }
        h2 { color: #34495e; border-bottom: 3px solid #3498db; padding-bottom: 10px; margin-top: 40px; }
        h3 { color: #2980b9; margin-top: 30px; }
        
        .metrics-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0; }
        .metric-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .metric-card h3 { margin: 0 0 15px 0; font-size: 1.3em; }
        .metric-value { font-size: 1.1em; margin: 8px 0; }
        
        .example { border: 2px solid #bdc3c7; margin: 30px 0; padding: 25px; border-radius: 10px; background: #fafafa; }
        .example-header { background: #34495e; color: white; padding: 15px; margin: -25px -25px 20px -25px; border-radius: 8px 8px 0 0; }
        .example-id { font-weight: bold; font-size: 1.2em; }
        
        .image-section { text-align: center; margin: 20px 0; }
        .example-image { max-width: 400px; max-height: 300px; border: 2px solid #34495e; border-radius: 8px; }
        
        .predictions { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin-top: 20px; }
        .prediction { background: white; border: 2px solid #3498db; border-radius: 8px; padding: 15px; }
        .prediction h4 { margin: 0 0 10px 0; color: #2980b9; font-size: 1.1em; }
        .prediction-text { font-style: italic; line-height: 1.4; color: #2c3e50; }
        
        .ground-truth { background: #e8f5e8; border: 2px solid #27ae60; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .ground-truth h4 { color: #27ae60; margin: 0 0 10px 0; }
        
        .stats { background: #ecf0f1; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .timestamp { text-align: center; color: #7f8c8d; margin-top: 30px; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Confronto Modelli Baseline</h1>
        <h2>📊 Riepilogo Metriche</h2>
        
        <div class="metrics-summary">

            <div class="metric-card">
                <h3>BLIP-2</h3>
                <div class="metric-value">📊 Esempi: 400</div>
                <div class="metric-value">🎯 BLEU-4: 0.0506</div>
                <div class="metric-value">⭐ METEOR: 0.2227</div>
                <div class="metric-value">🚀 CIDEr: 0.4428</div>
                <div class="metric-value">🖼️ CLIPScore: 32.54</div>
            </div>
        
            <div class="metric-card">
                <h3>Florence2</h3>
                <div class="metric-value">📊 Esempi: 400</div>
                <div class="metric-value">🎯 BLEU-4: 0.0396</div>
                <div class="metric-value">⭐ METEOR: 0.4150</div>
                <div class="metric-value">🚀 CIDEr: 0.4449</div>
                <div class="metric-value">🖼️ CLIPScore: 34.20</div>
            </div>
        
            <div class="metric-card">
                <h3>Idefics3</h3>
                <div class="metric-value">📊 Esempi: 100</div>
                <div class="metric-value">🎯 BLEU-4: 0.0789</div>
                <div class="metric-value">⭐ METEOR: 0.3836</div>
                <div class="metric-value">🚀 CIDEr: 1.5045</div>
                <div class="metric-value">🖼️ CLIPScore: 33.09</div>
            </div>
        
        </div>
        
        <h2>🔍 Esempi Qualitativi Comparativi</h2>
        <div class="stats">
            <strong>📋 Metodologia:</strong> Selezionati esempi casuali comuni a tutti i modelli per confronto diretto delle predizioni.
        </div>
    
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 1 - ID: 76</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a logo that consists of two main elements: a green circle and a combination of text</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">the recipe now logo on a white background
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows the RecipeNow logo on a white background with a green circle on the left side. The text reads "Recipenow" and the logo is accompanied by a coupon code.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image features a logo that consists of two main elements: a green circular shape and text. The green circular shape is positioned on the left side of the image and has a solid fill. The text is positioned to the right of the green circle and reads "RecipeNow" in a stylized font. The word "Recipe" is written in a black, serif font, while "Now" is written in a bold, orange font with a cursive style.

### Analysis and Description

#### Logo</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 2 - ID: 84</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a simple, minimalistic graphic design featuring a red cross symbol superimposed on a blue square</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">an icon of a check box with a cross through it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a red cross in the middle of a white circle, indicating that it is not allowed.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts a simple, flat, two-dimensional graphic of a red checkmark inside a square. The checkmark is diagonally oriented, with one arm pointing upwards and to the right, and the other arm pointing downwards and to the left. The checkmark is filled with a solid red color, and it is placed inside a square that has a blue border. The square itself is outlined with a black line, and the interior of the square is white. The background of the image is a light</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 3 - ID: 50</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a cartoon-style illustration of a person's head</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">an icon of a woman's face with an orange hair
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows an animated boy with red hair and a smile on his face, set against a white background.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts a cartoon-style illustration of a person's face. The face is characterized by the following features:

1. **Hair**: The person has short, curly hair that is depicted in a bright orange color. The curls are evenly distributed across the head, giving a lively and dynamic appearance.

2. **Eyes**: The eyes are depicted with a simple, round shape and are positioned symmetrically on either side of the face. The eyes are black, which provides a stark contrast against the lighter</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 4 - ID: 13</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts an open envelope with a letter inside</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">an envelope with an envelope icon on it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows an open envelope with a letter inside, suggesting that someone is sending an email. The envelope is a vector icon, with a white background and a blue border. The letter inside is a light blue color, and the envelope is slightly tilted to the side.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts an open envelope with a letter inside. The envelope is predominantly orange with a black outline. The flap of the envelope is red with a black outline, and it is partially open, revealing the contents inside. The letter inside the envelope is white with a black border. The letter has three horizontal lines on it, which could represent a signature or a placeholder for a signature.

The envelope and letter are the primary objects in the image. The envelope is rectangular with rounded corners, and the letter</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 5 - ID: 92</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a rectangular green sign with the number "51" prominently displayed in bold yellow text</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">a green and yellow sign with the number n 51 on it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a green and yellow sign with the number 51 on it against a white background.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts a rectangular green sign with the number "51" prominently displayed in bold yellow text. The number "51" is centered within the green background, and the font is large and easily readable. The sign has a border that is also green, and the overall design is simple and straightforward, with no additional text or graphics.

### Analysis and Description

#### Sign Details:
- **Shape and Color**: The sign is rectangular and green.
- **Text**: The number "51" is written</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 6 - ID: 42</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a simple, minimalist design of a medical symbol</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">a computer monitor with a cross on it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a black and white computer monitor with a plus sign on it, set against a white background.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts a simple, minimalist design of a computer monitor. The monitor is rectangular with rounded edges, and it is black with a white cross in the center. The cross is symmetrical, with equal arms extending from the center point. The monitor is placed on a stand, which consists of two vertical lines that support the monitor from the bottom. The stand is also black, matching the color of the monitor. The background of the image is white, providing a stark contrast to the black monitor and</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 7 - ID: 54</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts </div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">an icon of two people with a hat on their heads
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a pair of red glasses on a white background. The glasses have a classic design with a thin frame and round lenses. The lenses are slightly tinted and the frame is slightly curved. The background is a bright white, providing a stark contrast to the glasses.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts two cartoon-style figures. Both figures are outlined in a solid red color, and they appear to be standing upright. The figures are positioned side by side, with the one on the left slightly in front of the one on the right. Each figure has a head, a body, and limbs, but the details of the limbs are minimal and simplified, consisting only of lines indicating the presence of arms and legs. The heads of the figures are circular and have a small, rounded chin.</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 8 - ID: 19</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a rectangular shape with rounded corners, which appears to be a button or a label</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">a yellow button with the word motoko on it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a yellow motoko logo on a white background. The logo is in the center of the image and is composed of a bright yellow color.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image features a large, rectangular button with a solid orange background. The button has rounded edges, giving it a smooth and rounded appearance. The text "Motoko" is prominently displayed in the center of the button. The text is in a bold, serif font, with the letters "M" and "O" being slightly larger than the other letters, creating a visually appealing contrast. The color of the text is a dark brown, which stands out against the bright orange background, making the text</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 9 - ID: 25</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts </div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">a green and white button with trees on it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a green circle with trees in the middle of it, set against a gray background. The trees are animated, giving the image a lively feel.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image is a circular graphic featuring a green background with a white border. Within the green area, there are four pine trees depicted in a stylized manner. The trees are arranged in a triangular formation, with three trees on the left side and one on the right side. Each tree has a conical shape with multiple branches and needles, characteristic of pine trees. The trees are rooted in a brown soil base, which is visible at the bottom of the image.

The trees are rendered in a simple</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 10 - ID: 24</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a road sign with a blue background and a white border</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">a blue and white sign with the number 933 on it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a blue and white sign with the number 933 on it, which is a logo with the text "933" in the center.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image is a road sign featuring a blue background with a white number. The number prominently displayed is "93," which is written in bold, uppercase letters. The number is centered and occupies the majority of the space in the sign. Above the number "93," there are three small, blue, semi-circular shapes, each with a white outline. These shapes are evenly spaced and appear to be stylized representations of exhaust pipes or similar objects. The sign has a border that is also blue,</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 11 - ID: 4</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts - There is a logo in the image</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">the pcloud logo with a cloud in the center
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows the pcloud logo on a white background with the text "pcloud" underneath, indicating that it is a cloud-based service that allows users to securely store and manage their data.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image features a logo that consists of two primary elements: a circular emblem and text. The circular emblem is prominently placed on the left side of the image. It is a stylized cloud shape with a white background and a blue border. Inside the cloud, there is a white letter "P" in a sans-serif font. The letter "P" is centered and appears to be bold and uppercase. The cloud shape is designed to look like a cloud with rounded edges and a slight gradient effect,</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 12 - ID: 20</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a mathematical symbol, specifically the letter "S" with a unique and stylized design</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">a black and white image of a circle and the letter z
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a black and white clock with the letter 'S' in the center, surrounded by a circle of dots.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts a stylized letter "S" composed of a series of interconnected dots. The dots are arranged in a circular pattern, forming a continuous loop. The dots are uniformly spaced and evenly distributed around the curve of the letter, creating a smooth and flowing appearance. The letter "S" is written in a cursive or script style, with the tail of the "S" extending downward and curving back to connect with the main body of the letter. The dots are black, and the</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 13 - ID: 43</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a road sign with a rectangular shape and a black border</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">a black and white sign with the number 84 on it
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a black and white sign with the words "SP 84" on it against a white background.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts a rectangular sign with a black border and white background. The sign is a standard road sign, commonly used for indicating the number of a specific road or route. The number "84" is prominently displayed in large, bold black numerals in the center of the sign. The number is positioned vertically, with "8" on top and "4" on the bottom. Above the number, the letters "SP" are written in uppercase, likely indicating the abbreviation for "State Route"</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 14 - ID: 67</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a logo that consists of a square with rounded corners</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">the logo for markserv
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows the Markserv logo, which consists of a white background with the text "Markserv" written in a bold, black font. The logo is framed by a thin black border, giving it a modern and professional look.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image features a logo that consists of a combination of text and a graphical element. The text reads "MARKSERV" in lowercase letters, with each letter colored in a teal shade. The letters are arranged in a horizontal line, with the "M" slightly larger than the other letters. The "M" is positioned above the other letters, creating a slight offset.

The graphical element is a large, dark blue square with rounded corners. Inside the square, there is a white arrow pointing downwards</div>
                </div>
            
            </div>
        </div>
        
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio 15 - ID: 47</div>
            </div>
        
            <div class="ground-truth">
                <h4>🎯 Ground Truth:</h4>
                <div>The image depicts a yin-yang symbol, a traditional Chinese symbol that represents the harmony and balance between opposites</div>
            </div>
            
            <div class="predictions">
        
                <div class="prediction">
                    <h4>BLIP-2</h4>
                    <div class="prediction-text">the yin and yang symbol in rainbow colors
</div>
                </div>
            
                <div class="prediction">
                    <h4>Florence2</h4>
                    <div class="prediction-text">The image shows a yin-yang symbol with a rainbow of colors, representing the duality of the universe. The symbol is composed of two halves, one black and one white, with the black half representing the inner circle and the white half representing outer circle. The colors of the rainbow are vibrant and represent the diversity of the LGBT community.</div>
                </div>
            
                <div class="prediction">
                    <h4>Idefics3</h4>
                    <div class="prediction-text"> The image depicts a yin-yang symbol, a traditional Chinese symbol representing the harmony and interdependence of opposites. The yin-yang symbol is a circular shape divided into two halves, each containing a different color. The left half of the circle is black, while the right half is white. The black half contains a small white dot in the center, and the white half contains a small black dot in the center.

The yin-yang symbol is often associated with the concept of d</div>
                </div>
            
            </div>
        </div>
        
        <div class="timestamp">
            📅 Report generato il 04/07/2025 alle 19:07:41
        </div>
    </div>
</body>
</html>
    