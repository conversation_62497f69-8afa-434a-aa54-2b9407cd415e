<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Completo Modelli Trained - SVG Captioning</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .section {
            margin: 40px 0;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .section h2 {
            color: #495057;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        .radar-section {
            text-align: center;
            margin: 40px 0;
        }
        .radar-section img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .model-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
        }
        .model-card h3 {
            margin-top: 0;
            color: #007bff;
        }
        .metric-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .metric-row:last-child {
            border-bottom: none;
        }
        .metric-name {
            font-weight: bold;
        }
        .metric-value {
            color: #28a745;
            font-weight: bold;
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.completed {
            background: #d4edda;
            color: #155724;
        }
        .status.training {
            background: #fff3cd;
            color: #856404;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Report Modelli Trained</h1>
            <p>SVG Captioning - Risultati Training Completo</p>
            <p>Generato il: 2025-07-05 18:22:04</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 Performance Comparative</h2>
                <div class="radar-section">
                    <img src="trained_models_radar_20250705_182202.png" alt="Radar Chart Performance">
                    <p><em>Confronto performance tra i 3 modelli trained</em></p>
                </div>
            </div>
            
            <div class="section">
                <h2>📈 Metriche Dettagliate</h2>
                <div class="metrics-grid">
                    <div class="model-card">
                        <h3>Gemma T9</h3>
                        <div class="status training">Training in progress...</div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-1:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-2:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-3:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-4:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">METEOR:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CIDEr:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CLIPScore:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                    </div>
                    <div class="model-card">
                        <h3>Llama T8</h3>
                        <div class="status training">Training in progress...</div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-1:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-2:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-3:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-4:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">METEOR:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CIDEr:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CLIPScore:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                    </div>
                    <div class="model-card">
                        <h3>Llama T9</h3>
                        <div class="status training">Training in progress...</div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-1:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-2:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-3:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-4:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">METEOR:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CIDEr:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CLIPScore:</span>
                            <span class="metric-value">0.0000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🎯 Report generato automaticamente dal sistema di evaluation</p>
            <p>Versioni: Transformers 4.45.0 + Accelerate 1.2.1 + PEFT 0.4.0</p>
        </div>
    </div>
</body>
</html>