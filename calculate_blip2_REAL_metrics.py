#!/usr/bin/env python3
"""
Calcola metriche per BLIP-2 con immagini COLORATE corrette
"""

import json
import os
import numpy as np
from datetime import datetime
from shared.utils.metrics import CaptionEvaluator

def calculate_blip2_real_metrics():
    """Calcola metriche per BLIP-2 con immagini colorate"""
    
    # File risultati BLIP-2 con immagini colorate
    blip2_file = "evaluation_results/blip2_REAL_colors_results_20250704_134206.json"
    
    print(f"📂 Caricamento risultati BLIP-2: {blip2_file}")
    
    with open(blip2_file, 'r') as f:
        blip2_data = json.load(f)
    
    print(f"✅ Risultati caricati: {len(blip2_data['results'])} esempi")
    
    # Prepara dati per il calcolo delle metriche
    predictions = []
    references = []
    
    for result in blip2_data['results']:
        if result['success']:
            predictions.append(result['prediction'])
            references.append(result['ground_truth'])
    
    print(f"📊 Esempi validi per metriche: {len(predictions)}")
    
    # Calcola metriche
    calculator = CaptionEvaluator()

    print("🔄 Calcolo metriche...")

    # Prepara references come lista di liste (ogni prediction ha una lista di references)
    references_list = [[ref] for ref in references]

    # BLEU
    print("   🔄 BLEU...")
    bleu_scores = {}
    for i, (pred, ref_list) in enumerate(zip(predictions, references_list)):
        bleu = calculator.compute_bleu(ref_list, pred)
        for k, v in bleu.items():
            if k not in bleu_scores:
                bleu_scores[k] = []
            bleu_scores[k].append(v)

    # Media BLEU
    bleu_final = {k: np.mean(v) for k, v in bleu_scores.items()}

    # METEOR
    print("   🔄 METEOR...")
    meteor_score = calculator.compute_meteor(references_list, predictions)

    # CIDEr
    print("   🔄 CIDEr...")
    cider_score = calculator.compute_cider(references_list, predictions)

    # CLIPScore (usando image paths)
    print("   🔄 CLIPScore...")
    image_paths = [result['image_path'] for result in blip2_data['results'] if result['success']]
    clip_score = calculator.compute_clip_score(image_paths, predictions)

    # Calcola success rate e diversity
    success_rate = (len(predictions) / len(blip2_data['results'])) * 100

    # Diversity (unique predictions / total predictions)
    unique_predictions = len(set(predictions))
    diversity_score = unique_predictions / len(predictions) if predictions else 0.0
    
    # Risultati finali
    results = {
        'model': 'BLIP-2',
        'dataset': 'baseline_t7_corrected_400_REAL_colors_fixed.json',
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'total_examples': len(blip2_data['results']),
        'successful_examples': len(predictions),
        'success_rate': success_rate,
        'metrics': {
            'BLEU-1': bleu_final['bleu1'],
            'BLEU-2': bleu_final['bleu2'],
            'BLEU-3': bleu_final['bleu3'],
            'BLEU-4': bleu_final['bleu4'],
            'METEOR': meteor_score,
            'CIDEr': cider_score,
            'CLIPScore': clip_score,
            'Diversity': diversity_score
        }
    }
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"evaluation_results/blip2_REAL_colors_metrics_{timestamp}.json"
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n✅ Metriche calcolate e salvate: {output_file}")
    
    # Stampa risultati
    print(f"\n📊 RISULTATI BLIP-2 (IMMAGINI COLORATE):")
    print(f"   🎯 Success Rate: {success_rate:.1f}%")
    print(f"   📝 BLEU-4: {bleu_final['bleu4']:.4f}")
    print(f"   🌟 METEOR: {meteor_score:.4f}")
    print(f"   🎪 CIDEr: {cider_score:.4f}")
    print(f"   🖼️ CLIPScore: {clip_score:.4f}")
    print(f"   🎨 Diversity: {diversity_score:.4f}")
    
    return results

if __name__ == "__main__":
    calculate_blip2_real_metrics()
