#!/usr/bin/env python3
"""
Florence2 Inference con dataset CORRETTO (immagini colorate)
Per esecuzione su boost
"""

import json
import torch
from PIL import Image
from transformers import AutoProcessor, AutoModelForCausalLM
import os
from datetime import datetime

def load_model():
    """Carica il modello Florence2"""
    print("🔄 Caricamento Florence2...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    model = AutoModelForCausalLM.from_pretrained(
        "microsoft/Florence-2-large",
        torch_dtype=torch.float32,
        trust_remote_code=True
    ).to(device)
    
    processor = AutoProcessor.from_pretrained("microsoft/Florence-2-large", trust_remote_code=True)
    
    print("✅ Florence2 caricato!")
    return processor, model, device

def run_inference():
    """Esegue inferenza Florence2 con dataset corretto"""
    
    # Dataset CORRETTO con immagini colorate
    dataset_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_REAL_colors_fixed.json"
    
    print(f"📂 Caricamento dataset CORRETTO: {dataset_path}")
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    print(f"✅ Dataset caricato: {len(dataset)} esempi")
    print("🎨 USANDO IMMAGINI COLORATE CORRETTE!")
    
    # Carica modello
    processor, model, device = load_model()
    
    # Risultati
    results = []
    
    print("🚀 Inizio inferenza Florence2...")
    
    for i, item in enumerate(dataset):
        try:
            # Carica immagine COLORATA
            image_path = item['image_path']
            
            if not os.path.exists(image_path):
                print(f"❌ Immagine non trovata: {image_path}")
                continue
            
            image = Image.open(image_path).convert('RGB')
            
            # Prepara input per detailed caption
            prompt = "<DETAILED_CAPTION>"
            inputs = processor(text=prompt, images=image, return_tensors="pt").to(device)
            
            # Genera descrizione
            with torch.no_grad():
                generated_ids = model.generate(
                    input_ids=inputs["input_ids"],
                    pixel_values=inputs["pixel_values"],
                    max_new_tokens=1024,
                    num_beams=3,
                    do_sample=False
                )
                
                generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
                prediction = processor.post_process_generation(
                    generated_text, 
                    task="<DETAILED_CAPTION>", 
                    image_size=(image.width, image.height)
                )
                
                # Estrai il testo della caption
                if isinstance(prediction, dict) and '<DETAILED_CAPTION>' in prediction:
                    prediction_text = prediction['<DETAILED_CAPTION>']
                else:
                    prediction_text = str(prediction)
            
            # Salva risultato
            result = {
                'example_id': i,
                'image_path': image_path,
                'ground_truth': item['caption'],
                'prediction': prediction_text,
                'success': True
            }
            
            results.append(result)
            
            if (i + 1) % 50 == 0:
                print(f"   📊 Processati: {i + 1}/{len(dataset)}")
            
        except Exception as e:
            print(f"❌ Errore esempio {i}: {e}")
            
            # Salva errore
            result = {
                'example_id': i,
                'image_path': item.get('image_path', ''),
                'ground_truth': item.get('caption', ''),
                'prediction': '',
                'success': False,
                'error': str(e)
            }
            results.append(result)
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/florence2_REAL_colors_results_{timestamp}.json"
    
    os.makedirs("evaluation_results", exist_ok=True)
    
    output_data = {
        'model': 'Florence2',
        'dataset': 'baseline_t7_corrected_400_REAL_colors_fixed.json',
        'timestamp': timestamp,
        'total_examples': len(dataset),
        'successful_examples': len([r for r in results if r['success']]),
        'results': results
    }
    
    with open(output_path, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\n✅ Florence2 inference completata!")
    print(f"📁 Risultati salvati: {output_path}")
    print(f"📊 Esempi processati: {len([r for r in results if r['success']])}/{len(dataset)}")

if __name__ == "__main__":
    run_inference()
