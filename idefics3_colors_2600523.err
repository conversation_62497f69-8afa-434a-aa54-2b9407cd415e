usage: conda [-h] [--no-plugins] [-V] COMMAND ...
conda: error: argument COMMAND: invalid choice: 'activate' (choose from 'clean', 'compare', 'config', 'create', 'info', 'init', 'install', 'list', 'notices', 'package', 'remove', 'uninstall', 'rename', 'run', 'search', 'update', 'upgrade', 'build', 'content-trust', 'convert', 'debug', 'develop', 'doctor', 'index', 'inspect', 'metapackage', 'render', 'skeleton', 'env', 'verify', 'pack', 'token', 'repo', 'server')
The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.

Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:21<01:05, 21.86s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:44<00:44, 22.31s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [01:05<00:21, 21.52s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [01:12<00:00, 15.79s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [01:12<00:00, 18.02s/it]
/homes/ediluzio/.conda/envs/svg_env_new/lib/python3.10/site-packages/transformers/generation/utils.py:2497: UserWarning: You are calling .generate() with the `input_ids` being on a device type different than your model's device. `input_ids` is on cpu, whereas the model is on cuda. You may experience unexpected behaviors or slower generation. Please make sure that you have put `input_ids` to the correct device by calling for example input_ids = input_ids.to('cuda') before running `.generate()`.
  warnings.warn(
[2025-07-04T15:28:22.550] error: *** JOB 2600523 ON patagarro CANCELLED AT 2025-07-04T15:28:22 DUE to SIGNAL Terminated ***
