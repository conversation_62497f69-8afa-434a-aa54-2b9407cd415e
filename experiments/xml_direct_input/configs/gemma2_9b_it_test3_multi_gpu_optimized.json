{"_comment": "GEMMA2_9B_IT_TEST3_MULTI_GPU_OPTIMIZED - Score 0.3019", "_version": "TEST3_OPTIMIZED", "_created": "2025-01-07", "_description": "Configurazione ESATTA per il modello con score 0.3019 su WandB - CON gradient accumulation", "model_name_or_path": "google/gemma-2-9b-it", "model_type": "gemma2", "output_dir": "experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu", "data_file": "data/processed/xml_format_optimized/train_set_100k_final_90000.json", "val_file": "data/processed/xml_format_optimized/test_set_100k_final_10000.json", "_comment_dataset": "Dataset: 90K training + 10K validation examples", "dataset_size_train": 90000, "dataset_size_val": 10000, "task_type": "svg_to_caption", "_comment_batch": "Batch configuration CON gradient accumulation per performance ottimale", "per_device_train_batch_size": 1, "per_device_eval_batch_size": 1, "gradient_accumulation_steps": 16, "effective_batch_size": 32, "dataloader_num_workers": 4, "dataloader_pin_memory": true, "dataloader_drop_last": false, "_comment_training": "Training configuration ottimizzata per score 0.3019", "max_steps": 45000, "max_length": 2048, "eval_steps": 500, "save_steps": 250, "logging_steps": 50, "max_eval_samples": 10000, "prediction_loss_only": false, "_comment_optimizer": "Optimizer configuration per score 0.3019", "learning_rate": 0.0001, "lr_scheduler_type": "cosine_with_restarts", "warmup_ratio": 0.05, "weight_decay": 0.01, "optim": "adamw_torch_fused", "adam_beta1": 0.9, "adam_beta2": 0.999, "adam_epsilon": 1e-08, "max_grad_norm": 0.5, "_comment_lora": "LoRA configuration ESATTA per score 0.3019", "lora_r": 64, "lora_alpha": 128, "lora_dropout": 0.1, "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"], "lora_bias": "none", "lora_task_type": "CAUSAL_LM", "_comment_quantization": "Quantizzazione 4-bit (disabilitata per distributed)", "load_in_4bit": true, "load_in_8bit": false, "bnb_4bit_compute_dtype": "float16", "bnb_4bit_quant_type": "nf4", "bnb_4bit_use_double_quant": true, "_comment_precision": "Mixed precision training", "fp16": true, "bf16": false, "tf32": true, "gradient_checkpointing": true, "remove_unused_columns": false, "_comment_distributed": "Distributed training configuration", "ddp_backend": "nccl", "ddp_find_unused_parameters": false, "ddp_bucket_cap_mb": 25, "ddp_broadcast_buffers": false, "_comment_logging": "Logging and monitoring", "report_to": ["wandb"], "logging_dir": "logs", "logging_strategy": "steps", "evaluation_strategy": "steps", "save_strategy": "steps", "save_total_limit": 5, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "_comment_early_stopping": "Early stopping configuration", "early_stopping_patience": 10, "early_stopping_threshold": 0.001, "_comment_memory": "Memory optimization per dual-GPU >24GB", "max_memory_MB": 46000, "cpu_offload": false, "pin_memory": true, "_comment_generation": "Generation parameters per inference", "generation_max_length": 150, "generation_num_beams": 1, "generation_do_sample": true, "generation_temperature": 0.7, "generation_top_p": 0.9, "generation_top_k": 50, "generation_repetition_penalty": 1.1, "_comment_gemma2_specific": "Gemma 2 specific optimizations", "use_flash_attention_2": true, "torch_dtype": "float16", "attn_implementation": "flash_attention_2", "sliding_window_attention": 4096, "soft_capping": 50.0, "final_logit_softcapping": 30.0, "_comment_hardware": "Hardware specifications per score 0.3019", "target_gpu": "L40S", "gpu_memory_gb": 48, "num_gpus": 2, "cpu_cores": 16, "ram_gb": 64, "_comment_performance": "Performance targets per score 0.3019", "target_training_time_hours": 24, "target_steps": 36200, "estimated_steps_per_hour": 1508, "checkpoint_frequency_minutes": 30, "_comment_final": "Configurazione ESATTA per gemma2_9b_it_test3_multi_gpu_optimized con score 0.3019"}