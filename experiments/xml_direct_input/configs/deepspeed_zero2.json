{"zero_optimization": {"stage": 2, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "allgather_partitions": true, "allgather_bucket_size": 200000000.0, "overlap_comm": true, "reduce_scatter": true, "reduce_bucket_size": 200000000.0, "contiguous_gradients": true}, "gradient_accumulation_steps": "auto", "gradient_clipping": "auto", "steps_per_print": 25, "train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "wall_clock_breakdown": false, "bf16": {"enabled": false}, "fp16": {"enabled": true, "loss_scale": 0, "loss_scale_window": 1000, "initial_scale_power": 16, "hysteresis": 2, "min_loss_scale": 1}}