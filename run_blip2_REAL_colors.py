#!/usr/bin/env python3
"""
Script per eseguire BLIP2 sui dati REAL colors per baseline
Ricreato dopo eliminazione accidentale
"""

import json
import torch
from PIL import Image
from transformers import Blip2Processor, Blip2ForConditionalGeneration
import os
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("🎯 BLIP2 BASELINE EVALUATION")
    print("============================")
    
    # Carica modello BLIP2
    logger.info("Caricamento BLIP2...")
    processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b")
    model = Blip2ForConditionalGeneration.from_pretrained("Salesforce/blip2-opt-2.7b", torch_dtype=torch.float16)
    
    if torch.cuda.is_available():
        model = model.to("cuda")
        logger.info("✅ BLIP2 caricato su GPU")
    
    # Dataset
    dataset_path = "data/processed/xml_format_optimized/baseline_t7_corrected_400_REAL_colors_fixed.json"
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(dataset)} esempi")
    
    # Risultati
    results = {
        "model": "BLIP-2",
        "dataset": "baseline_t7_corrected_400_REAL_colors_fixed.json",
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "total_examples": len(dataset),
        "successful_examples": 0,
        "results": []
    }
    
    # Elabora esempi
    for i, example in enumerate(dataset):
        try:
            # Carica immagine
            image_path = example.get("image_path", "")
            if not os.path.exists(image_path):
                logger.warning(f"⚠️ Immagine non trovata: {image_path}")
                continue
                
            image = Image.open(image_path).convert("RGB")
            
            # Genera caption
            inputs = processor(image, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = {k: v.to("cuda") for k, v in inputs.items()}
            
            with torch.no_grad():
                generated_ids = model.generate(**inputs, max_length=50)
                generated_caption = processor.decode(generated_ids[0], skip_special_tokens=True)
            
            # Salva risultato
            result = {
                "example_id": i,
                "image_path": image_path,
                "ground_truth": example.get("caption", ""),
                "generated_caption": generated_caption.strip(),
                "xml_content": example.get("xml_content", "")
            }
            
            results["results"].append(result)
            results["successful_examples"] += 1
            
            if (i + 1) % 50 == 0:
                logger.info(f"📈 Processati {i + 1}/{len(dataset)} esempi")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/blip2_REAL_colors_results_{timestamp}.json"
    
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Risultati salvati: {output_path}")
    logger.info(f"📊 Esempi processati: {results['successful_examples']}/{results['total_examples']}")

if __name__ == "__main__":
    main()
