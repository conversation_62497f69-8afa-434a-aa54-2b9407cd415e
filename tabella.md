# 📊 PARAMETRI E CONFIGURAZIONI COMPLETE - SVG CAPTIONING MODELS

## 🏆 MODELLI CON SCORE ELEVATI (WandB)

### 🥇 GEMMA2_9B_IT_TEST3_MULTI_GPU_OPTIMIZED - Score: 0.3019

| Parametro | Valore |
|-----------|--------|
| **WandB Score** | **0.3019** (CLIP Score) |
| **Checkpoint** | checkpoint-36200 (36,200 steps) |
| **Gradient Accumulation** | **CON accumulation** (differenza chiave) |
| **Training Time** | ~24 ore su dual-GPU |
| **Memory Usage** | ~28-30GB per GPU |
| **Quantization** | SENZA quantization (--disable_quantization) |
| **Multi-GPU** | torch.distributed.run --nproc_per_node=2 |
| **Master Port** | 29502 |

### 🥈 TEST4_GEMMA_20250517_111231 - Score: 0.2972

| Parametro | Valore |
|-----------|--------|
| **WandB Score** | **0.2972** |
| **Data Training** | 2025-05-17 |
| **Configurazione** | Simile a 0.3019 ma performance inferiore |

### 🥉 GEMMA2_9B_IT_TEST3_MULTI_GPU_OPTIMIZED - Score: 0.2708

| Parametro | Valore |
|-----------|--------|
| **WandB Score** | **0.2708** |
| **Configurazione** | Variante del modello 0.3019 |

---

## 🔧 MODEL ARCHITECTURE DETAILS

### GEMMA T9 (google/gemma-2-9b-it)

| Parametro | Gemma T9 |
|-----------|----------|
| Nome modello | google/gemma-2-9b-it |
| Parametri totali | 9.24B |
| Architettura | Gemma-2 |
| Context Length | 8,192 token |
| Vocab Size | 256,000 |
| Layers | 42 |
| Attention Heads | 16 |
| Hidden Size | 3,584 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |
| Attention Type | Multi-Head Attention |
| Activation Function | GeGLU |
| Normalization | RMSNorm |
| Position Encoding | RoPE (Rotary Position Embedding) |

### LLAMA T8 (meta-llama/Llama-3.1-8B-Instruct)

| Parametro | Llama T8 |
|-----------|----------|
| Nome modello | meta-llama/Llama-3.1-8B-Instruct |
| Parametri totali | 8.03B |
| Architettura | Llama-3.1 |
| Context Length | 131,072 token |
| Vocab Size | 128,256 |
| Layers | 32 |
| Attention Heads | 32 |
| Hidden Size | 4,096 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |
| Attention Type | Grouped Query Attention (GQA) |
| Activation Function | SwiGLU |
| Normalization | RMSNorm |
| Position Encoding | RoPE (Rotary Position Embedding) |

### LLAMA T9 (meta-llama/Llama-3.1-8B-Instruct)

| Parametro | Llama T9 |
|-----------|----------|
| Nome modello | meta-llama/Llama-3.1-8B-Instruct |
| Parametri totali | 8.03B |
| Architettura | Llama-3.1 |
| Context Length | 131,072 token |
| Vocab Size | 128,256 |
| Layers | 32 |
| Attention Heads | 32 |
| Hidden Size | 4,096 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |
| Attention Type | Grouped Query Attention (GQA) |
| Activation Function | SwiGLU |
| Normalization | RMSNorm |
| Position Encoding | RoPE (Rotary Position Embedding) |

---

## 🎯 TRAINING CONFIGURATION

### GEMMA T9

| Parametro | Gemma T9 |
|-----------|----------|
| Max Steps | **50,000** |
| Eval Steps | **250** |
| Save Steps | **250** |
| Logging Steps | **25** |
| Max Eval Samples | **500** |
| Max Length (token) | 2,048 |
| Gradient Accumulation | **1** (NO accumulation) |
| Batch Size per device | **1** |
| Effective Batch Size | **2** (Dual GPU) |
| Num Train Epochs | Auto (basato su steps) |
| Dataloader Drop Last | False |
| Dataloader Num Workers | 4 |
| Remove Unused Columns | False |
| Label Smoothing | 0.0 |

### GEMMA T8 (Score 0.3019 - CON Gradient Accumulation)

| Parametro | Gemma T8 |
|-----------|----------|
| Max Steps | **50,000** |
| Eval Steps | **250** |
| Save Steps | **250** |
| Logging Steps | **25** |
| Max Eval Samples | **500** |
| Max Length (token) | 2,048 |
| Gradient Accumulation | 8 **CON accumulation** (differenza chiave) |
| Batch Size per device | **1** |
| Effective Batch Size | **Variabile** (con accumulation) |
| Num Train Epochs | Auto (basato su steps) |
| Dataloader Drop Last | False |
| Dataloader Num Workers | 4 |
| Remove Unused Columns | False |
| Label Smoothing | 0.0 |
| **Quantization** | **SENZA quantization** (--disable_quantization) |
| **Multi-GPU** | torch.distributed.run --nproc_per_node=2 |

### LLAMA T8

| Parametro | Llama T8 |
|-----------|----------|
| Max Steps | **50,000** |
| Eval Steps | **250** |
| Save Steps | **250** |
| Logging Steps | **25** |
| Max Eval Samples | **500** |
| Max Length (token) | 2,048 |
| Gradient Accumulation | **8** (CON accumulation) |
| Batch Size per device | **1** |
| Effective Batch Size | **16** (Dual GPU × 8 accum) |
| Num Train Epochs | Auto (basato su steps) |
| Dataloader Drop Last | False |
| Dataloader Num Workers | 4 |
| Remove Unused Columns | False |
| Label Smoothing | 0.0 |

### LLAMA T9

| Parametro | Llama T9 |
|-----------|----------|
| Max Steps | **50,000** |
| Eval Steps | **250** |
| Save Steps | **250** |
| Logging Steps | **25** |
| Max Eval Samples | **500** |
| Max Length (token) | 2,048 |
| Gradient Accumulation | **1** (NO accumulation) |
| Batch Size per device | **1** |
| Effective Batch Size | **2** (Dual GPU) |
| Num Train Epochs | Auto (basato su steps) |
| Dataloader Drop Last | False |
| Dataloader Num Workers | 4 |
| Remove Unused Columns | False |
| Label Smoothing | 0.0 |

---

## ⚙️ OPTIMIZER / LR SCHEDULING

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| Optimizer | AdamW |
| Learning Rate | **5e-5** |
| LR Scheduler | Cosine |
| Warmup Steps | **1,000** |
| Warmup Ratio | **0.02** |
| Weight Decay | 0.01 |
| Adam Beta1 | 0.9 |
| Adam Beta2 | 0.999 |
| Adam Epsilon | 1e-8 |
| Max Grad Norm | 1.0 |
| LR Scheduler Type | cosine |
| Cosine Restarts | False |
| Min LR Ratio | 0.1 |

---

## 🔧 LoRA CONFIGURATION

### TUTTI I MODELLI (CONFIGURAZIONE CORRETTA)

| Parametro | Valore |
|-----------|--------|
| **LoRA Rank** | **64** |
| **LoRA Alpha** | **128** |
| **LoRA Dropout** | **0.05** |
| **Target Modules** | **q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj** |
| **LoRA Bias** | None |
| **PEFT Type** | LORA |
| **Task Type** | CAUSAL_LM |
| **Fan in Fan out** | False |
| **Init LoRA Weights** | True |
| **Use RSLoRA** | False |
| **Use DoRA** | False |
| **LoRA Alpha/Rank Ratio** | **2.0** |

---

## 💾 QUANTIZZAZIONE

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| Load in 4bit | True |
| Compute Dtype | float16 |
| Double Quant | True |
| Quant Type | nf4 |
| Quant Storage | uint8 |
| BNB 4bit Use Double Quant | True |
| BNB 4bit Quant Type | nf4 |
| BNB 4bit Compute Dtype | float16 |
| BNBQ Config | BitsAndBytesConfig |

---

## 🖥️ HARDWARE E INFRASTRUTTURA

### TUTTI I MODELLI

| Parametro | Valore |
|-----------|--------|
| **GPU Setup** | **Dual GPU (CUDA:0,1)** |
| GPU Memory | ~48GB VRAM per GPU (L40S) |
| CPU Cores | 16 |
| RAM | 64GB |
| Partizione SLURM | boost_usr_prod |
| Account SLURM | tesi_ediluzio |
| QOS | normal |
| Time Limit | 24:00:00 |
| Nodes | 1 |
| Tasks per Node | 1 |
| GPUs per Node | 2 |

---

## 🔧 ENVIRONMENT VERSIONS (CORRETTE)

### STACK SOFTWARE FUNZIONANTE

| Componente | Versione | Status |
|------------|----------|--------|
| **Transformers** | **4.45.0** | ✅ Stabile |
| **Accelerate** | **1.2.1** | ✅ Compatibile |
| **PEFT** | **0.16.0** | ✅ Aggiornato |
| **PyTorch** | 2.7.1 | ✅ CUDA 12.6 |
| **CUDA** | 12.6 | ✅ Con libs fix |
| **Python** | 3.10.18 | ✅ Conda env |
| **WandB** | 0.20.1 | ✅ Logging attivo |
| **DeepSpeed** | ≥0.12.0 | ✅ Per Stage 2/3 |

---

## 📊 TRAINING STATUS ATTUALE (2025-07-11)

### GEMMA T9 (Job 2602760)

| Parametro | Valore |
|-----------|--------|
| Job ID | 2602760 |
| Status | **PENDING** (in coda) |
| Script | RESUME_gemma_t9_from_checkpoint.slurm |
| Checkpoint | checkpoint-3750 |
| Output Dir | experiments/xml_direct_input/outputs/gemma_t9_no_accumulation |

### LLAMA T8 (Job 2603013)

| Parametro | Valore |
|-----------|--------|
| Job ID | 2603013 |
| Status | **SUBMITTED** (nuovo) |
| Script | RESUME_llama_t8_from_checkpoint.slurm |
| Checkpoint | checkpoint-4500 (4750 era corrotto) |
| Output Dir | experiments/xml_direct_input/outputs/llama_t8_24h |

### LLAMA T9 (Job 2602327)

| Parametro | Valore |
|-----------|--------|
| Job ID | 2602327 |
| Status | **RUNNING** (1h52m) |
| Script | RESUME_llama_t9_from_checkpoint.slurm |
| Checkpoint | checkpoint-5500 |
| Output Dir | experiments/xml_direct_input/outputs/llama_t9_no_accumulation |

---

## 📋 DATASET CONFIGURATION

| Parametro | Valore |
|-----------|--------|
| File Training | train_set_100k_final_90000.json (90,000 esempi) |
| File Validation | test_set_100k_final_10000.json (10,000 esempi) |
| Formato Input | XML SVG ottimizzato + caption in linguaggio naturale |
| Max SVG Length | 1,800 caratteri |
| Max Caption Length | 200 caratteri |
| Preprocessing | Tokenizzazione con padding dinamico |
| Data Path | data/processed/xml_format_optimized/ |
| Tokenizer Padding | True |
| Tokenizer Truncation | True |
| Add Special Tokens | True |


## 🔧 ENVIRONMENT VARIABLES

### TUTTI I MODELLI

| Variabile | Valore |
|-----------|--------|
| WANDB_DISABLED | false |
| WANDB_PROJECT | svg_captioning |
| TOKENIZERS_PARALLELISM | false |
| CUDA_LAUNCH_BLOCKING | 1 |
| LD_LIBRARY_PATH | /usr/local/cuda/lib64:$LD_LIBRARY_PATH |
| CUDA_HOME | /usr/local/cuda |
| TORCH_CUDA_ARCH_LIST | 7.0;7.5;8.0;8.6 |
| CUDA_VISIBLE_DEVICES | 0,1 |
| HUGGINGFACE_HUB_TOKEN | ************************************* |

---

## 📁 OUTPUT DIRECTORIES

### CHECKPOINT PATHS

| Modello | Output Directory |
|---------|------------------|
| **Gemma T9** | experiments/xml_direct_input/outputs/gemma_t9_no_accumulation |
| **Llama T8** | experiments/xml_direct_input/outputs/llama_t8_24h |
| **Llama T9** | experiments/xml_direct_input/outputs/llama_t9_no_accumulation |

### CONFIG PATHS

| Modello | Config File |
|---------|-------------|
| **Gemma T9** | experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json |
| **Llama T8** | experiments/xml_direct_input/configs/llama_t8_final_optimized.json |
| **Llama T9** | experiments/xml_direct_input/configs/llama_t9_no_accumulation.json |

---

## 🚀 DEEPSPEED CONFIGURATIONS

### STAGE 2 (Meno aggressivo)

| Parametro | Valore |
|-----------|--------|
| Stage | 2 |
| Offload Optimizer | CPU |
| Offload Parameters | No (restano su GPU) |
| Memory Savings | ~50% |
| Speed Impact | ~15% slower |

### STAGE 3 (Più aggressivo)

| Parametro | Valore |
|-----------|--------|
| Stage | 3 |
| Offload Optimizer | CPU |
| Offload Parameters | CPU |
| Memory Savings | ~80-90% |
| Speed Impact | ~40% slower |

---

## 📊 QUOTA E STORAGE

| Parametro | Valore |
|-----------|--------|
| Spazio Totale Progetto | 78GB (dopo pulizia) |
| Checkpoint Rimasti | 6 checkpoint (2 per modello) |
| Spazio Liberato | 15GB (pulizia conservativa) |
| Filesystem Disponibile | 87TB |

---

*Tabella aggiornata: 2025-07-11 - Tutte le configurazioni verificate e corrette*
