joblib==1.5.1
bitsandbytes==0.46.0
peft==0.4.0
typing-inspection==0.4.1
threadpoolctl==3.6.0
smmap==5.0.2
setproctitle==1.3.6
scipy==1.15.3
pydantic_core==2.33.2
protobuf==6.31.1
platformdirs==4.3.8
scikit-learn==1.7.0
annotated-types==0.7.0
sentry-sdk==2.31.0
pydantic==2.11.7
gitdb==4.0.12
GitPython==3.1.44
wandb==0.20.1
wheel==0.45.1
pip==25.1
nvidia-cusparselt-cu12==0.6.3
xxhash==3.5.0
tzdata==2025.2
typing_extensions==4.14.1
triton==3.3.1
sympy==1.14.0
smmap==5.0.2
six==1.17.0
setproctitle==1.3.6
safetensors==0.5.3
regex==2024.11.6
MarkupSafe==3.0.2
pyarrow==20.0.0
protobuf==6.31.1
propcache==0.3.2
platformdirs==4.3.8
pillow==11.2.1
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==*********
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cublas-cu12==********
numpy==2.2.6
hf-xet==1.1.5
frozenlist==1.7.0
certifi==2025.6.15
dill==0.3.8
click==8.2.1
multidict==6.6.3
multidict==6.4.4
async-timeout==5.0.1
annotated-types==0.7.0
aiohappyeyeballs==2.6.1
typing-inspection==0.4.1
sentry-sdk==2.30.0
requests==2.32.4
python-dateutil==2.9.0.post0
pydantic_core==2.33.2
nvidia-cusparse-cu12==********
nvidia-cufft-cu12==********
nvidia-cudnn-cu12==********
multiprocess==0.70.16
accelerate==0.26.0
gitdb==4.0.12
aiosignal==1.3.2
aiosignal==1.4.0
yarl==1.20.1
pydantic==2.11.7
pandas==2.3.0
nvidia-cusolver-cu12==********
GitPython==3.1.44
wandb==0.20.1
pyarrow-hotfix==0.7
torch==2.7.1
aiohttp==3.12.13
mpmath==1.3.0
torchvision==0.22.1
torchmetrics==1.7.3
peft==0.15.2
datasets==3.6.0
datasets==2.20.0
webencodings==0.5.1
wcwidth==0.2.13
omegaconf==2.3.0
timm==1.0.15
text-unidecode==1.3
sentencepiece==0.2.0
pure_eval==0.2.3
ptyprocess==0.7.0
distlib==0.3.9
cymem==2.0.11
contexttimer==0.3.3
braceexpand==0.1.7
antlr4-python3-runtime==4.9.3
wrapt==1.17.2
webdataset==0.2.111
watchdog==6.0.0
wasabi==1.1.3
virtualenv==20.31.2
traitlets==5.14.3
tornado==6.5.1
toml==0.10.2
tifffile==2025.5.10
tenacity==9.1.2
spacy-loggers==1.0.5
spacy-legacy==3.0.12
shellingham==1.5.4
scipy==1.15.3
rpds-py==0.25.1
python-slugify==8.0.4
python-magic==0.4.27
Pygments==2.19.1
pycocotools==2.0.10
prompt_toolkit==3.0.51
portalocker==3.2.0
pexpect==4.9.0
parso==0.8.4
networkx==3.4.2
opencv-python-headless==********
nodeenv==1.9.1
narwhals==1.43.0
murmurhash==1.0.13
mdurl==0.1.2
marisa-trie==1.2.1
imageio==2.37.0
identify==2.6.12
ftfy==6.3.1
executing==2.2.0
exceptiongroup==1.3.0
einops==0.8.1
decord==0.6.0
decorator==5.2.1
cloudpathlib==0.21.1
cfgv==3.4.0
catalogue==2.0.10
cachetools==5.5.2
blis==1.3.0
blinker==1.9.0
bleach==6.2.0
asttokens==3.0.0
stack-data==0.6.3
srsly==2.5.1
smart-open==7.1.0
referencing==0.36.2
pydeck==0.9.1
pycocoevalcap==1.2
preshed==3.0.10
pre_commit==4.2.0
bitsandbytes==0.46.0
joblib==1.5.1
nltk==3.9.1
attrs==25.3.0
pyparsing==3.2.3
kiwisolver==1.4.8
fonttools==4.58.4
cycler==0.12.1
contourpy==1.3.2
matplotlib==3.10.3
threadpoolctl==3.6.0
scikit-learn==1.7.0
absl-py==2.3.0
rouge_score==0.1.2
tensorboardX==2.6.4
tqdm==4.67.1
packaging==25.0
Jinja2==3.1.6
huggingface-hub==0.33.2
clip-score==0.2.1
tokenizers==0.21.2
pytz==2025.2
PyYAML==6.0.2
fsspec==2024.5.0
filelock==3.18.0
setuptools==80.9.0
psutil==7.0.0
lightning-utilities==0.14.3
transformers==4.53.1
urllib3==2.5.0
idna==3.10
charset-normalizer==3.4.2
platformdirs==4.2.2
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.45.1
zipp==3.19.2
