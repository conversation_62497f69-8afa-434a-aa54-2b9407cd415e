_comment:
    value: GEMMA T9 - Configurazione SENZA Gradient Accumulation
_comment_batch:
    value: Batch configuration SENZA gradient accumulation per L40S (48GB each)
_comment_dataset:
    value: 'Dataset: 90K training + 10K validation examples from 100K SVG-caption pairs'
_comment_distributed:
    value: Distributed training configuration per dual-GPU
_comment_early_stopping:
    value: Early stopping per evitare overfitting
_comment_evaluation:
    value: Evaluation and saving strategy
_comment_evaluation_metrics:
    value: Evaluation metrics to be computed
_comment_final:
    value: Configurazione T9 Gemma con gradient_accumulation_steps=1 (NO accumulation)
_comment_generation:
    value: Generation parameters per inference
_comment_hardware:
    value: Hardware specifications
_comment_logging:
    value: Logging e monitoring configuration
_comment_lora:
    value: LoRA configuration ottimizzata per Gemma-2-9B
_comment_memory:
    value: Memory optimization settings per L40S
_comment_model_specs:
    value: Model architecture specifications
_comment_optimizer:
    value: Optimizer configuration con cosine scheduling
_comment_performance:
    value: Performance targets
_comment_precision:
    value: Mixed precision training configuration
_comment_quantization:
    value: 4-bit quantization con BitsAndBytes per memory efficiency
_comment_reproducibility:
    value: Reproducibility settings
_comment_training:
    value: Training configuration per 24h su dual-GPU
_comment_training_data:
    value: Training data specifications
_created:
    value: "2025-06-18"
_description:
    value: Training Gemma-2-9B-IT per SVG captioning con gradient_accumulation_steps=1 (NO accumulation)
_version:
    value: T9.0
_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.10.18
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 63
                - 71
                - 98
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 63
                - 71
                - 98
            "3":
                - 13
                - 16
                - 55
            "4": 3.10.18
            "5": 0.20.1
            "6": 4.53.1
            "12": 0.20.1
            "13": linux-x86_64
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
best_checkpoint_dir:
    value: null
bf16:
    value: false
bnb_4bit_compute_dtype:
    value: float16
bnb_4bit_quant_storage:
    value: uint8
bnb_4bit_quant_type:
    value: nf4
bnb_4bit_use_double_quant:
    value: true
checkpoint_frequency_minutes:
    value: 60
context_length:
    value: 8192
cpu_cores:
    value: 16
cpu_offload:
    value: false
data_file:
    value: data/processed/xml_format_optimized/train_set_100k_final_90000.json
data_seed:
    value: 42
dataloader_drop_last:
    value: false
dataloader_num_workers:
    value: 4
dataloader_pin_memory:
    value: true
dataset:
    value: data/processed/xml_format_optimized/train_set_100k_final_90000.json
dataset_size_train:
    value: 90000
dataset_size_val:
    value: 10000
ddp_backend:
    value: nccl
ddp_broadcast_buffers:
    value: false
ddp_bucket_cap_mb:
    value: 25
ddp_find_unused_parameters:
    value: false
early_stopping:
    value: false
early_stopping_patience:
    value: 15
early_stopping_threshold:
    value: 0.0005
effective_batch_size:
    value: 2
entity:
    value: 337543-unimore
estimated_steps_per_hour:
    value: 2083
estimated_total_time_hours:
    value: 24
eval_metrics:
    value:
        - loss
        - perplexity
        - bleu4
        - cider
        - rouge_l
        - clip_score
eval_steps:
    value: 250
evaluation_strategy:
    value: steps
fp16:
    value: true
generation_do_sample:
    value: true
generation_max_length:
    value: 150
generation_num_beams:
    value: 1
generation_repetition_penalty:
    value: 1.1
generation_temperature:
    value: 0.7
generation_top_k:
    value: 50
generation_top_p:
    value: 0.9
gpu_memory_gb:
    value: 48
gradient_accumulation_steps:
    value: 1
gradient_checkpointing:
    value: true
greater_is_better:
    value: false
hidden_size:
    value: 3584
input_format:
    value: SVG XML
learning_rate:
    value: 5e-05
load_best_model_at_end:
    value: true
load_in_4bit:
    value: true
local_rank:
    value: -1
logging_dir:
    value: experiments/xml_direct_input/logs/gemma_t9
logging_first_step:
    value: true
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 25
lora_alpha:
    value: 128
lora_bias:
    value: none
lora_dropout:
    value: 0.05
lora_r:
    value: 64
lora_target_modules:
    value:
        - q_proj
        - k_proj
        - v_proj
        - o_proj
        - gate_proj
        - up_proj
        - down_proj
lora_task_type:
    value: CAUSAL_LM
lr_scheduler_type:
    value: cosine
max_caption_length:
    value: 200
max_eval_samples:
    value: 500
max_grad_norm:
    value: 1
max_length:
    value: 2048
max_memory_MB:
    value: 46000
max_steps:
    value: 50000
max_svg_length:
    value: 1800
metric_for_best_model:
    value: eval_loss
min_delta:
    value: null
model_architecture:
    value: Gemma-2
model_name:
    value: google/gemma-2-9b-it
model_name_or_path:
    value: google/gemma-2-9b-it
model_size_parameters:
    value: 9B
model_type:
    value: gemma2
num_attention_heads:
    value: 16
num_gpus:
    value: 2
num_layers:
    value: 42
optim:
    value: adamw_torch
output_dir:
    value: experiments/xml_direct_input/outputs/gemma_t9_no_accumulation
output_format:
    value: Natural language caption
patience:
    value: null
per_device_eval_batch_size:
    value: 1
per_device_train_batch_size:
    value: 1
pin_memory:
    value: true
prediction_loss_only:
    value: false
preprocessing:
    value: XML format optimized
ram_gb:
    value: 64
remove_unused_columns:
    value: false
report_to:
    value: wandb
run_name:
    value: gemma_t9_no_accumulation_24h
save_safetensors:
    value: true
save_steps:
    value: 250
save_strategy:
    value: steps
save_total_limit:
    value: 5
seed:
    value: 42
target_gpu:
    value: L40S
target_training_time_hours:
    value: 24
task_type:
    value: svg_to_caption
tf32:
    value: true
torch_compile:
    value: false
trainable_params:
    value: 216072192
trainable_percentage:
    value: 2.2845977985432504
transformers_seed:
    value: 42
val_file:
    value: data/processed/xml_format_optimized/test_set_100k_final_10000.json
vocab_size:
    value: 256000
warmup_ratio:
    value: 0.02
warmup_steps:
    value: 1000
weight_decay:
    value: 0.01
