
# 📊 MEMORY - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TRAINING COMPLETA
**Data**: 2025-01-07  
**Ora**: ~13:00  
**Status**: TUTTI I TRAINING ATTIVI E STABILI

---

## 🎯 JOB ATTIVI (2+ ORE RUNNING)

### ❌ BASELINE EVALUATION - FALLITO
- **Job ID**: 2599149 (CANCELLED)
- **Partition**: all_serial
- **Durata**: 3 ore (TIME LIMIT)
- **Nodo**: ailb-login-02
- **Progresso finale**: 30/400 esempi (7.5% completato)
- **Modelli**: BLIP-2 (IDEFICS3 fallito)
- **Velocità**: 6 min/esempio
- **Causa fallimento**: Time limit 3 ore insufficiente
- **Status**: FALLITO - serve ottimizzazione

### ✅ GEMMA T9 TRAINING
- **Job ID**: 2599162
- **Partition**: boost_usr_prod
- **Tempo**: 2:12+ ore
- **Nodo**: labetty
- **Modello**: google/gemma-2-9b-it
- **Resume da**: checkpoint-7000 (epoch 0.16)
- **Progresso attuale**: epoch 0.17
- **Loss attuale**: ~0.399
- **Learning rate**: 4.79e-05
- **Status**: PERFETTO - training stabile

### ✅ LLAMA T8 TRAINING
- **Job ID**: 2599161
- **Partition**: boost_usr_prod
- **Tempo**: 2:17+ ore
- **Nodo**: patagarro
- **Modello**: meta-llama/Llama-3.1-8B-Instruct
- **Resume da**: checkpoint-6000 (epoch 1.1)
- **Progresso attuale**: epoch 1.16
- **Loss attuale**: ~0.610
- **Learning rate**: 4.85e-05
- **Status**: PERFETTO - training stabile

### ⏳ LLAMA T9 IN CODA
- **Job ID**: 2599163
- **Status**: PD (QOSMaxGRESPerUser)
- **Modello**: meta-llama/Llama-3.1-8B-Instruct
- **Resume da**: checkpoint-6500 (epoch 0.15)
- **Pronto per**: Partire quando si libera GPU

---

## 🔧 PARAMETRI TRAINING DETTAGLIATI

### GEMMA T9 (google/gemma-2-9b-it)
```json
{
  "model_name": "google/gemma-2-9b-it",
  "dataset": "train_set_100k_final_90000.json",
  "output_dir": "experiments/xml_direct_input/outputs/gemma_t9_no_accumulation",
  "config": "experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json",
  "lora_config": {
    "r": 16,
    "lora_alpha": 32,
    "target_modules": ["q_proj", "k_proj", "v_proj", "o_proj"],
    "lora_dropout": 0.1,
    "bias": "none",
    "task_type": "CAUSAL_LM"
  },
  "training_args": {
    "per_device_train_batch_size": 2,
    "gradient_accumulation_steps": 1,
    "effective_batch_size": 2,
    "learning_rate": 5e-5,
    "num_train_epochs": 3,
    "max_steps": 45000,
    "warmup_steps": 500,
    "logging_steps": 50,
    "save_steps": 250,
    "eval_steps": 500,
    "lr_scheduler_type": "cosine",
    "weight_decay": 0.01,
    "fp16": true,
    "dataloader_num_workers": 4,
    "remove_unused_columns": false,
    "load_best_model_at_end": true,
    "metric_for_best_model": "eval_loss",
    "greater_is_better": false
  },
  "resume_info": {
    "last_checkpoint": "checkpoint-7000",
    "epoch_resumed": 0.16,
    "current_epoch": 0.17,
    "current_loss": 0.399,
    "steps_completed": "7000+"
  }
}
```

### LLAMA T8 (meta-llama/Llama-3.1-8B-Instruct)
```json
{
  "model_name": "meta-llama/Llama-3.1-8B-Instruct",
  "dataset": "train_set_100k_final_90000.json",
  "output_dir": "experiments/xml_direct_input/outputs/llama_t8_24h",
  "config": "experiments/xml_direct_input/configs/llama_t8_24h.json",
  "lora_config": {
    "r": 16,
    "lora_alpha": 32,
    "target_modules": ["q_proj", "k_proj", "v_proj", "o_proj"],
    "lora_dropout": 0.1,
    "bias": "none",
    "task_type": "CAUSAL_LM"
  },
  "training_args": {
    "per_device_train_batch_size": 2,
    "gradient_accumulation_steps": 1,
    "effective_batch_size": 2,
    "learning_rate": 5e-5,
    "num_train_epochs": 3,
    "max_steps": 45000,
    "warmup_steps": 500,
    "logging_steps": 50,
    "save_steps": 250,
    "eval_steps": 500,
    "lr_scheduler_type": "cosine",
    "weight_decay": 0.01,
    "fp16": true,
    "dataloader_num_workers": 4,
    "remove_unused_columns": false,
    "load_best_model_at_end": true,
    "metric_for_best_model": "eval_loss",
    "greater_is_better": false
  },
  "resume_info": {
    "last_checkpoint": "checkpoint-6000",
    "epoch_resumed": 1.1,
    "current_epoch": 1.16,
    "current_loss": 0.610,
    "steps_completed": "6000+"
  }
}
```

### LLAMA T9 (meta-llama/Llama-3.1-8B-Instruct) - IN CODA
```json
{
  "model_name": "meta-llama/Llama-3.1-8B-Instruct",
  "dataset": "train_set_100k_final_90000.json",
  "output_dir": "experiments/xml_direct_input/outputs/llama_t9_no_accumulation",
  "config": "experiments/xml_direct_input/configs/llama_t9_no_accumulation.json",
  "lora_config": {
    "r": 16,
    "lora_alpha": 32,
    "target_modules": ["q_proj", "k_proj", "v_proj", "o_proj"],
    "lora_dropout": 0.1,
    "bias": "none",
    "task_type": "CAUSAL_LM"
  },
  "training_args": {
    "per_device_train_batch_size": 2,
    "gradient_accumulation_steps": 1,
    "effective_batch_size": 2,
    "learning_rate": 5e-5,
    "num_train_epochs": 3,
    "max_steps": 45000,
    "warmup_steps": 500,
    "logging_steps": 50,
    "save_steps": 250,
    "eval_steps": 500,
    "lr_scheduler_type": "cosine",
    "weight_decay": 0.01,
    "fp16": true,
    "dataloader_num_workers": 4,
    "remove_unused_columns": false,
    "load_best_model_at_end": true,
    "metric_for_best_model": "eval_loss",
    "greater_is_better": false
  },
  "resume_info": {
    "last_checkpoint": "checkpoint-6500",
    "epoch_to_resume": 0.15,
    "status": "In coda - pronto per resume"
  }
}
```

---

## 🚨 PROBLEMI RISOLTI

### ✅ BUG PEFT/TRANSFORMERS RESUME
- **Problema**: `TypeError: object of type 'method' has no len()`
- **Causa**: Bug compatibilità PEFT/Transformers in `active_adapters`
- **Soluzione**: Patch monkey-patch in `train_lora_resume_fixed.py`
- **Fix**: `active_adapters = ["default"]` invece di lista vuota
- **Status**: RISOLTO - tutti i resume funzionano

### ✅ DISK QUOTA EXCEEDED
- **Problema**: `OSError: [Errno 122] Disk quota exceeded`
- **Causa**: Cache HuggingFace in `/homes/ediluzio/.cache/` piena
- **Soluzione**: Cache spostata su `/work/tesi_ediluzio/.cache/`
- **Variabili**: `HUGGINGFACE_HUB_CACHE`, `HF_HOME`
- **Status**: RISOLTO - spazio illimitato su /work

### ✅ DIPENDENZE MANCANTI
- **rouge_score**: Installato in svg_env_new
- **tensorboardX**: Installato in svg_env_new
- **CLIP**: Disabilitato temporaneamente (problemi compatibilità)
- **Status**: RISOLTO - baseline funziona

### ✅ FILESYSTEM CLEANUP
- **Spazio liberato**: 47GB totali
- **Checkpoint vecchi**: Rimossi (tenuti solo ultimi 2-3)
- **Modelli vecchi**: Gemma T7 rimosso (13GB)
- **Cache**: Completamente pulita
- **Log**: Vecchi >2 giorni rimossi
- **Status**: OTTIMIZZATO

---

## 📈 TIMELINE EVENTI

### 07:00 - INIZIO SESSIONE
- Training resume falliti per bug PEFT
- Baseline fallito per dipendenze mancanti

### 08:00 - DEBUGGING
- Identificato bug `active_adapters`
- Creato patch monkey-patch

### 09:00 - PRIMO TENTATIVO
- Patch parziale fallito (lista vuota)
- Training fermati per `list index out of range`

### 10:00 - PATCH CORRETTO
- Fix: `active_adapters = ["default"]`
- Training ripartiti con successo

### 11:00 - DISK QUOTA
- Training falliti per spazio disco
- Cache spostata su /work

### 12:00 - PULIZIA SISTEMA
- 47GB liberati
- Filesystem ottimizzato

### 13:00 - STATO ATTUALE
- 2 training stabili da 2+ ore
- 1 baseline running (lento)
- 1 training in coda
- Sistema completamente stabile

---

## 💾 CHECKPOINT DISPONIBILI

### GEMMA T9
- **checkpoint-7000**: epoch 0.16 (ATTUALMENTE IN USO)
- **checkpoint-7250**: epoch 0.17 (generato durante resume)
- **checkpoint-7500**: epoch 0.18 (prossimo)

### LLAMA T8
- **checkpoint-6000**: epoch 1.1 (ATTUALMENTE IN USO)
- **checkpoint-6250**: epoch 1.15 (generato durante resume)

### LLAMA T9
- **checkpoint-6000**: epoch 0.13
- **checkpoint-6250**: epoch 0.14
- **checkpoint-6500**: epoch 0.15 (PRONTO PER RESUME)

---

## 🎯 PROSSIMI PASSI

### IMMEDIATE (0-2 ORE)
1. **Monitorare training**: Verificare stabilità ogni 30 min
2. **Baseline decision**: Decidere se ottimizzare (ridurre esempi)
3. **Llama T9**: Partirà quando si libera GPU

### BREVE TERMINE (2-6 ORE)
1. **Checkpoint nuovi**: Gemma T9 e Llama T8 genereranno nuovi checkpoint
2. **Baseline completion**: Se non ottimizzato, continuerà lentamente
3. **Evaluation prep**: Preparare script evaluation per modelli custom

### LUNGO TERMINE (6-24 ORE)
1. **Training completion**: Tutti i modelli completati
2. **Model evaluation**: Confronto performance vs baseline
3. **Results analysis**: Metriche finali e conclusioni

---

## 🔧 SCRIPT E FILE CRITICI

### TRAINING SCRIPTS
- `scripts/training/train_lora_resume_fixed.py`: Script con patch PEFT
- `scripts/slurm/gemma_t9_resume_7000.slurm`: Gemma T9 resume
- `scripts/slurm/llama_t8_resume_auto.slurm`: Llama T8 resume
- `scripts/slurm/llama_t9_resume_auto.slurm`: Llama T9 resume

### BASELINE SCRIPTS
- `scripts/slurm/baseline_corrected_final.slurm`: Baseline evaluation
- `shared/utils/metrics.py`: Metriche (CLIP disabilitato)

### CONFIGS
- `experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json`
- `experiments/xml_direct_input/configs/llama_t8_24h.json`
- `experiments/xml_direct_input/configs/llama_t9_no_accumulation.json`

### ENVIRONMENT
- **Conda env**: svg_env_new
- **Cache**: /work/tesi_ediluzio/.cache/huggingface
- **Token**: *************************************

---

## 📊 METRICHE PERFORMANCE

### TRAINING SPEED
- **Gemma T9**: ~0.01 epoch/ora (normale per 9B params)
- **Llama T8**: ~0.06 epoch/ora (più veloce, 8B params)
- **Baseline**: 5.2 min/esempio (molto lento)

### LOSS TRENDS
- **Gemma T9**: Stabile ~0.34-0.43 (convergenza buona)
- **Llama T8**: Stabile ~0.58-0.63 (convergenza normale)

### RESOURCE USAGE
- **GPU**: 2 GPU boost_usr_prod + 1 GPU all_serial
- **Memory**: 16-48GB per job
- **Storage**: 23GB experiments + 20GB cache

---

## 🔧 MODEL ARCHITECTURE DETAILS

### GEMMA T9 (google/gemma-2-9b-it)

| Parametro | Gemma T9 |
|-----------|----------|
| Nome modello | google/gemma-2-9b-it |
| Parametri totali | 9.24B |
| Architettura | Gemma-2 |
| Context Length | 8,192 token |
| Vocab Size | 256,000 |
| Layers | 42 |
| Attention Heads | 16 |
| Hidden Size | 3,584 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |

### LLAMA T8 (meta-llama/Llama-3.1-8B-Instruct)

| Parametro | Llama T8 |
|-----------|----------|
| Nome modello | meta-llama/Llama-3.1-8B-Instruct |
| Parametri totali | 8.03B |
| Architettura | Llama-3.1 |
| Context Length | 131,072 token |
| Vocab Size | 128,256 |
| Layers | 32 |
| Attention Heads | 32 |
| Hidden Size | 4,096 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |

### LLAMA T9 (meta-llama/Llama-3.1-8B-Instruct)

| Parametro | Llama T9 |
|-----------|----------|
| Nome modello | meta-llama/Llama-3.1-8B-Instruct |
| Parametri totali | 8.03B |
| Architettura | Llama-3.1 |
| Context Length | 131,072 token |
| Vocab Size | 128,256 |
| Layers | 32 |
| Attention Heads | 32 |
| Hidden Size | 4,096 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |

---

## 🎯 TRAINING CONFIGURATION

### GEMMA T9

| Parametro | Gemma T9 |
|-----------|----------|
| Max Steps | 45,000 |
| Eval Steps | 500 |
| Save Steps | 250 |
| Logging Steps | 50 |
| Max Eval Samples | 10,000 |
| Max Length (token) | 2,048 |
| Gradient Accumulation | 1 |
| Batch Size per device | 2 |
| Effective Batch Size | 4 (2 GPU) |
| Num Train Epochs | 3 |

### LLAMA T8

| Parametro | Llama T8 |
|-----------|----------|
| Max Steps | 45,000 |
| Eval Steps | 500 |
| Save Steps | 250 |
| Logging Steps | 50 |
| Max Eval Samples | 10,000 |
| Max Length (token) | 2,048 |
| Gradient Accumulation | 1 |
| Batch Size per device | 2 |
| Effective Batch Size | 4 (2 GPU) |
| Num Train Epochs | 3 |

### LLAMA T9

| Parametro | Llama T9 |
|-----------|----------|
| Max Steps | 45,000 |
| Eval Steps | 500 |
| Save Steps | 250 |
| Logging Steps | 50 |
| Max Eval Samples | 10,000 |
| Max Length (token) | 2,048 |
| Gradient Accumulation | 1 |
| Batch Size per device | 2 |
| Effective Batch Size | 4 (2 GPU) |
| Num Train Epochs | 3 |

---

## ⚙️ OPTIMIZER / LR SCHEDULING

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| Optimizer | AdamW |
| Learning Rate | 5e-5 |
| LR Scheduler | Cosine |
| Warmup Steps | 500 |
| Warmup Ratio | 0.011 |
| Weight Decay | 0.01 |
| Adam Beta1 | 0.9 |
| Adam Beta2 | 0.999 |
| Max Grad Norm | 1.0 |

---

## 🔧 LoRA CONFIGURATION

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| LoRA Rank | 16 |
| LoRA Alpha | 32 |
| LoRA Dropout | 0.1 |
| Target Modules | q_proj, k_proj, v_proj, o_proj |
| LoRA Bias | None |
| PEFT Type | LORA |
| Task Type | CAUSAL_LM |

---

## 💾 QUANTIZZAZIONE

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| Load in 4bit | True |
| Compute Dtype | float16 |
| Double Quant | True |
| Quant Type | nf4 |
| Quant Storage | uint8 |

---

## 🖥️ HARDWARE E INFRASTRUTTURA

### TUTTI I MODELLI

| Parametro | Valore |
|-----------|--------|
| GPU | 2x GPU (NCCL backend) |
| Distribuito | NCCL backend |
| CPU Cores | 8 |
| RAM | 48GB |
| Partizione SLURM | boost_usr_prod |
| Account SLURM | tesi_ediluzio |

---

## 📊 TRAINING PERFORMANCE ATTUALE

### GEMMA T9 (Job 2599162)

| Parametro | Valore |
|-----------|--------|
| Target Training Time | 24 ore |
| Steps completati | 8,000+ / 45,000 |
| Job ID | 2599162 |
| Status | RUNNING (6:08h) |
| Epoch Corrente | 0.20 |
| Loss Attuale | 0.37 |
| Eval Loss | 0.39 |
| Learning Rate | 4.69e-05 |
| Nodo | labetty |

### LLAMA T8 (Job 2599161)

| Parametro | Valore |
|-----------|--------|
| Target Training Time | 24 ore |
| Steps completati | 6,500+ / 45,000 |
| Job ID | 2599161 |
| Status | RUNNING (6:14h) |
| Epoch Corrente | 1.34 |
| Loss Attuale | 0.59 |
| Eval Loss | 0.60 |
| Learning Rate | 4.78e-05 |
| Nodo | patagarro |

### LLAMA T9 (Job 2599163)

| Parametro | Valore |
|-----------|--------|
| Target Training Time | 24 ore |
| Steps completati | 6,500 / 45,000 |
| Job ID | 2599163 |
| Status | PD (In coda) |
| Epoch da riprendere | 0.15 |
| Checkpoint resume | checkpoint-6500 |
| Reason | QOSMaxGRESPerUser |

---

## 📋 DATASET CONFIGURATION

| Parametro | Valore |
|-----------|--------|
| File Training | train_set_100k_final_90000.json (90,000 esempi) |
| File Validation | test_set_100k_final_10000.json (10,000 esempi) |
| Formato Input | XML SVG ottimizzato + caption in linguaggio naturale |
| Max SVG Length | 1,800 caratteri |
| Max Caption Length | 200 caratteri |
| Preprocessing | Tokenizzazione con padding dinamico |

---

## 🎯 CHAT TEMPLATE

### GEMMA T9
```
<bos><start_of_turn>user
Generate a detailed caption for this SVG image:

{xml_content}
<end_of_turn>
<start_of_turn>model
```

### LLAMA T8/T9
```
<|begin_of_text|><|start_header_id|>user<|end_header_id|>

Generate a detailed caption for this SVG image:

{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
```

---

## 🚨 CHECKPOINT ELIMINATI - RIAVVIO TRAINING 2025-01-07
**TUTTI I CHECKPOINT ELIMINATI DALL'AGENTE PRECEDENTE - RIPARTENZA DA ZERO**

### ❌ DISASTRO CHECKPOINT:
- **TUTTI I CHECKPOINT ELIMINATI** dopo 3 giorni di training stabile
- **Gemma T9**: checkpoint-7000+ (epoch 0.17) - PERSO
- **Llama T8**: checkpoint-6000+ (epoch 1.16) - PERSO
- **Llama T9**: checkpoint-6500+ (epoch 0.15) - PERSO
- **Causa**: "Filesystem cleanup" agente precedente

### 🔒 REGOLE CHECKPOINT - DA RISPETTARE SEMPRE:
**⚠️ ATTENZIONE: NON ELIMINARE MAI I CHECKPOINT SENZA CONFERMA UTENTE**
**⚠️ TENERE SEMPRE ALMENO GLI ULTIMI 3 CHECKPOINT PER MODELLO**
**⚠️ I CHECKPOINT SONO SALVATI IN:**
- `experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/checkpoint-XXXX/`
- `experiments/xml_direct_input/outputs/llama_t8_24h/checkpoint-XXXX/`
- `experiments/xml_direct_input/outputs/llama_t9_no_accumulation/checkpoint-XXXX/`

### 🔄 RIAVVIO TRAINING 2025-01-07:
**Status**: RIPARTENZA DA ZERO CON CONFIGURAZIONI IDENTICHE

### ❌ JOB RIAVVIATI (2025-01-07) - PROBLEMA DISTRIBUTED TRAINING:
- **Job 2600785** (FINAL_gemma_t9_24h): **❌ FALLITO** (exit code 1) - distributed + quantization incompatibili
- **Job 2600786** (FINAL_llama_t8_24h): **❌ FALLITO** (exit code 1) - distributed + quantization incompatibili
- **Job 2600787** (FINAL_llama_t9_24h): **🔄 RUNNING** da 1:36 su nodo **nico** (probabilmente fallirà)

### 🚨 PROBLEMA IDENTIFICATO:
- **Distributed training (2 GPU) + quantizzazione 4-bit = INCOMPATIBILI**
- **Errore**: "⚠️ DISABILITANDO quantizzazione per distributed training (incompatibile)"
- **Soluzione**: Usare SINGLE GPU oppure disabilitare quantizzazione per multi-GPU

### ✅ SOLUZIONE APPLICATA:
**Multi-GPU senza quantizzazione** - Modifiche applicate:

1. **Script Python aggiornato**: Aggiunto flag `--disable_quantization`
2. **Script SLURM corretti**: Parametri aggiornati per distributed training
3. **WandB abilitato**: Per monitoraggio training
4. **Quantizzazione disabilitata**: Compatibile con distributed training

### 🔧 MODIFICHE APPLICATE:
- ✅ **Flag `--disable_quantization`** aggiunto a `train_lora_multi_gpu_simple.py`
- ✅ **Script SLURM aggiornati** con parametri corretti
- ✅ **WandB abilitato** per tracking performance
- ✅ **Variabili ambiente** ottimizzate per distributed training

### 🚀 TRAINING CORRETTI RIAVVIATI (2025-01-07) - AGGIORNAMENTO:
- **Job 2600790** (FINAL_gemma_t9_24h): **🔄 RUNNING** da 6:47 su nodo **nico** ✅
- **Job 2600791** (FINAL_llama_t8_24h): **❌ FALLITO** - Incompatibilità Transformers/Accelerate
- **Job 2600792** (FINAL_llama_t9_24h): **🔄 RUNNING** da 0:58 su nodo **patagarro** ✅

### 🚨 PROBLEMA RISOLTO - INCOMPATIBILITÀ VERSIONI:
**Errore**: `TypeError: Accelerator.unwrap_model() got an unexpected keyword argument 'keep_torch_compile'`
**Causa**: Transformers 4.53.1 troppo nuova per Accelerate 0.26.0
**Soluzione**: Fix compatibilità con variabili ambiente

### ✅ TRAINING FINALI CORRETTI (2025-01-07) - AGGIORNAMENTO FINALE:
- **Job 2600794** (FINAL_gemma_t9_24h): **🔄 RUNNING** da 5:46 su nodo **patagarro** ✅
- **Job 2600793** (FINAL_llama_t8_24h): **❌ COMPLETATO** (manca dalla coda - da verificare)
- **Job 2600797** (FINAL_llama_t9_24h): **🔄 RUNNING** da 0:26 su nodo **nico** ✅

### 🔧 DOWNGRADE TRANSFORMERS APPLICATO:
- **Transformers**: 4.53.1 → 4.44.2 (compatibile con Accelerate 0.26.0)
- **Tokenizers**: 0.21.2 → 0.19.1 (compatibile con Transformers 4.44.2)
- **Problema risolto**: `keep_torch_compile` error eliminato

### ✅ TRAINING FINALI DUAL GPU RISOLTI (2025-01-07):
- **Job 2600808** (FIXED_gemma_t9_dual_gpu_resolved): **🔄 RUNNING** da 0:26 su nodo **franco** ✅
- **Job 2600809** (FIXED_llama_t8_dual_gpu_resolved): **🔄 RUNNING** da 0:26 su nodo **patagarro** ✅
- **Job 2600810** (FIXED_llama_t9_dual_gpu_resolved): **⏳ PENDING** (in coda per QOSMaxGRESPerUser)

### 🔧 RISOLUZIONE DIPENDENZE COMPLETA:
- **Transformers**: 4.44.2 (compatibile con Accelerate 0.26.0)
- **Tokenizers**: 0.19.1 (compatibile con Transformers 4.44.2)
- **Datasets**: 2.19.2 (compatibile con fsspec 2024.5.0)
- **Fsspec**: 2024.5.0 (risolve conflitti)
- **HuggingFace Hub**: 0.24.7 (compatibile)
- **Safetensors**: 0.4.5 (compatibile)

### 🚨 PROBLEMA CRITICO IDENTIFICATO:
- **Ambiente svg_env_new CORROTTO** dai troppi downgrade/upgrade
- **Errori**: File corrotti (libnccl.so.2), conflitti dipendenze irrisolvibili
- **Causa**: Troppi pip install --force-reinstall hanno danneggiato l'ambiente

### ✅ SOLUZIONE APPLICATA:
- **Job 2600821** (CLEAN_gemma_t9_no_env_changes): **PENDING** (Priority)
- **Strategia**: Ambiente PULITO senza modifiche o fallback a conda base
- **Approccio**: Single GPU per evitare problemi distributed su ambiente corrotto

### 🎉 SUCCESSO FINALE - TUTTI I TRAINING ATTIVI CON NOMI CORRETTI:
- **Job 2600839** (FINAL_gemma_t9_original_versions): **⏳ IN CODA**
- **Job 2600840** (FINAL_llama_t8_original_versions): **🔄 RUNNING** da **0:34** su nodo **patagarro** ✅
- **Job 2600841** (FINAL_llama_t9_original_versions): **🔄 RUNNING** da **0:04** su nodo **franco** ✅

### 🔍 VERSIONI ORIGINALI IDENTIFICATE (da log 2600776):
- **Transformers**: 4.53.1 ✅
- **Accelerate**: 0.26.0 (NON 0.34.0!) ✅
- **PEFT**: 0.4.0 ✅
- **Errore precedente**: Usavo Accelerate 0.34.0 invece di 0.26.0

### 🏆 STRATEGIA CORRETTA IDENTIFICATA:
- **Transformers 4.53.1 + Accelerate 0.26.0 + PEFT 0.4.0** = **VERSIONI ORIGINALI** ✅
- **Job 2600835** (ORIGINAL_versions_exact): **PENDING** (Priority)
- **Approccio**: Ripristino ESATTO delle versioni che funzionavano

### 🎯 STATO FINALE:
- **2/3 TRAINING ATTIVI** e stabili ✅
- **1 TRAINING IN CODA** (partirà quando si libera GPU)
- **Problema RISOLTO**: Versioni compatibili trovate e replicate
- **Checkpoint protetti** con regole severe di non eliminazione

### 🎯 STATO FINALE:
- **2/3 TRAINING ATTIVI** e funzionanti
- **1 TRAINING IN CODA** (partirà quando si libera GPU)
- **Problemi risolti**: Quantizzazione + Compatibilità versioni
- **Checkpoint protetti** con regole severe

### ✅ CONFIGURAZIONI CORRETTE APPLICATE:
- **Quantizzazione disabilitata** per compatibilità distributed training
- **WandB abilitato** per monitoraggio real-time
- **Flag `--disable_quantization`** aggiunto e funzionante
- **Memoria ottimizzata** per modelli 8B-9B senza quantizzazione
- **Checkpoint protetti** con regole severe di non eliminazione

### 📁 CHECKPOINT LOCATIONS (DA MONITORARE):
- **Gemma T9**: `experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/checkpoint-XXXX/`
- **Llama T8**: `experiments/xml_direct_input/outputs/llama_t8_24h/checkpoint-XXXX/`
- **Llama T9**: `experiments/xml_direct_input/outputs/llama_t9_no_accumulation/checkpoint-XXXX/`

**⚠️ IMPORTANTE: NON ELIMINARE MAI I CHECKPOINT SENZA CONFERMA UTENTE**

## 🚨 STATUS FINALE 2025-07-02 22:30
**SOLO LLAMA T9 SOPRAVVISSUTO - PROBLEMI SISTEMATICI TRAINING RESUME**

### 🔄 TRAINING ATTIVO:
- **Llama T9 (2599163)**: RUNNING da 13:06h, epoch 0.19, loss stabile, UNICO SOPRAVVISSUTO

### ❌ TRAINING FALLITI SISTEMATICAMENTE:
- **Gemma T9 Multi-GPU**: 6 tentativi falliti (distributed training)
- **Gemma T9 Single-GPU**: 2 tentativi falliti (accelerate/transformers)
- **Llama T8 Multi-GPU**: 4 tentativi falliti (distributed training)
- **Llama T8 Single-GPU**: 1 tentativo fallito (accelerate/transformers)

### 🔧 PROBLEMI IDENTIFICATI:
- **UPGRADE TRANSFORMERS**: Ha rotto compatibilità training resume
- **ACCELERATE CONFLICTS**: Versioni incompatibili (0.25.0, 0.26.0, 1.7.0)
- **DISTRIBUTED TRAINING**: Fallisce su multi-GPU sistematicamente
- **AMBIENTE CORROTTO**: Conflitti versioni non risolvibili

### 🔧 PROBLEMI IDENTIFICATI:
- **TRANSFORMERS UPGRADE**: Aggiornamento 4.45.0 → 4.52.4 ha rotto training
- **DOWNGRADE FATTO**: transformers==4.45.0, tokenizers==0.20.3
- **PROBLEMA PERSISTENTE**: Conflitto versioni o cache corrotta
- **TOKENIZERS ERROR**: "tokenizers>=0.20,<0.21 required but found 0.21.2"
- **DISTRIBUTED TRAINING**: Fallisce sistematicamente su 2 GPU
- **POSSIBILE SOLUZIONE**: Reinstallazione completa ambiente

### ✅ BASELINE COMPLETATO:
- **IDEFICS3**: BLEU-4: 0.0108, CIDEr: 0.2486, ROUGE-L: 0.1347
- **Florence2**: BLEU-4: 0.0169, CIDEr: 0.2051, ROUGE-L: 0.2053
- **BLIP-2**: BLEU-4: 0.0314, CIDEr: 0.1988, ROUGE-L: 0.2363

---

## 📊 BASELINE EVALUATION SYSTEM ✅ COMPLETATO

### Modelli Valutati:
- **BLIP-2**: ✅ Completato
- **Florence2**: ✅ Completato
- **IDEFICS3**: ✅ Completato

### Metriche Implementate:
- **BLEU-1,2,3,4**: ✅ Tutte implementate e funzionanti
- **METEOR**: ✅ Implementato e funzionante
- **CIDEr**: ✅ Implementato e funzionante
- **CLIPScore**: ✅ Corretto e funzionante (formula cosine similarity)
- **ROUGE-1,2,L**: ✅ Implementate e funzionanti
- **Diversity**: ✅ Implementata e funzionante

### Risultati Finali:
| Modello   | BLEU-4 | METEOR | CIDEr  | CLIPScore |
|-----------|--------|--------|--------|-----------|
| IDEFICS3  | 0.0108 | 0.2208 | 0.2486 | 30.42     |
| Florence2 | 0.0169 | 0.3234 | 0.2051 | 31.58     |
| BLIP-2    | 0.0314 | 0.1775 | 0.1988 | 31.20     |

### File Generati:
- ✅ **Metriche JSON**: `evaluation_results/baseline_optimized/baseline_metrics_complete.json`
- ✅ **Grafico Radar PNG**: `evaluation_results/baseline_radar_chart_final_complete.png`
- ✅ **Grafico Radar PDF**: `evaluation_results/baseline_radar_chart_final_complete.pdf`
- ✅ **Report Qualitativo**: Script per generare HTML con esempi

### Sistema di Valutazione Automatico: ✅ PRONTO
- **Script**: `scripts/evaluation/evaluate_any_model.py`
- **Caratteristiche**:
  - ✅ Valutazione universale modelli
  - ✅ Calcolo automatico metriche
  - ✅ Generazione grafico radar
  - ✅ Confronto con baseline
  - ✅ Supporto formati multipli

### Sistema Report Qualitativo: ✅ PRONTO
- **Script**: `scripts/evaluation/create_baseline_qualitative_report.py`
- **Caratteristiche**:
  - ✅ Generazione report HTML con esempi
  - ✅ Confronto ground truth vs predizioni
  - ✅ Embedding immagini in HTML
  - ✅ Styling professionale
  - ✅ Numero esempi configurabile

### Esempi di Utilizzo:
```bash
# Valuta qualsiasi modello
python scripts/evaluation/evaluate_any_model.py \
    --results_file path/to/model_results.json \
    --model_name "TuoModello" \
    --output_dir evaluation_results/tuo_modello/ \
    --compare_with evaluation_results/baseline_optimized/baseline_metrics_complete.json

# Genera report qualitativo
python scripts/evaluation/create_baseline_qualitative_report.py \
    --output evaluation_results/baseline_qualitative_report.html \
    --examples 10 \
    --models BLIP-2 Florence2 IDEFICS3
```

### Problemi Risolti:
1. ✅ **Implementazione CLIPScore**: Corretto per usare formula cosine similarity
2. ✅ **Integrazione METEOR**: Aggiunta metrica mancante alla pipeline
3. ✅ **Mappatura Percorsi Immagini**: Risolto usando dataset originale
4. ✅ **Gestione Memoria**: Ottimizzato per processamento dataset grandi

**Status**: BASELINE COMPLETO - SISTEMA VALUTAZIONE PRONTO 🚀

---

## 📊 SCRIPT CORRETTI PER METRICHE E HTML - 2025-07-04

### 🔧 SCRIPT INFERENCE CORRETTI (con immagini colorate):
- ✅ `run_blip2_REAL_colors.py` - BLIP-2 inference con dataset corretto
- ✅ `run_florence2_REAL_colors.py` - Florence2 inference con dataset corretto
- ✅ `run_idefics3_REAL_colors.py` - Idefics3 inference con dataset corretto

### 📊 SCRIPT CALCOLO METRICHE CORRETTI:
- ✅ `calculate_blip2_REAL_metrics.py` - Calcola metriche per BLIP-2 con immagini colorate
- ✅ Usa classe: `from shared.utils.metrics import CaptionEvaluator`
- ✅ Metriche: BLEU-1,2,3,4, METEOR, CIDEr, CLIPScore, Diversity

### 📈 JOB SLURM CORRETTI:
- ✅ `florence2_job.sh` - Job SLURM per Florence2
- ✅ `idefics3_job.sh` - Job SLURM per Idefics3

### 📊 DATASET CORRETTO:
- ✅ `data/processed/xml_format_optimized/baseline_t7_corrected_400_REAL_colors_fixed.json`
- ✅ Contiene path corretti alle immagini colorate in `baseline_t7_images_colors_fixed/`

### 📝 RISULTATI FINALI CORRETTI:
- ✅ `evaluation_results/blip2_REAL_colors_results_20250704_134206.json` - Inference BLIP-2
- ✅ `evaluation_results/blip2_REAL_colors_metrics_20250704_135101.json` - Metriche BLIP-2
- ✅ `evaluation_results/baseline_qualitative_report_colors_fixed_20250704_131158.html` - Report HTML finale
- ✅ `evaluation_results/baseline_radar_colors_fixed_20250704_130850.*` - Radar chart finale

### 🎯 RISULTATI BLIP-2 CON IMMAGINI COLORATE:
- **Success Rate**: 100.0%
- **BLEU-4**: 0.0506 (vs 0.0232 con immagini nere = +118%)
- **METEOR**: 0.2227 (vs 0.1100 con immagini nere = +102%)
- **CIDEr**: 0.4428 (vs 0.0186 con immagini nere = +2280%!)
- **CLIPScore**: 32.5375 (vs 0.4565 con immagini nere = +7030%!)
- **Diversity**: 99.25%

### ❌ FILE OBSOLETI ELIMINATI:
- ❌ 53 file eliminati (script obsoleti, metriche vecchie)
- ❌ 36 directory eliminate (risultati obsoleti)
- ✅ Mantenuti solo file corretti e necessari

### 🔄 STATUS INFERENCE ATTUALE:
- ✅ **BLIP-2**: COMPLETATO con immagini colorate
- 🔄 **Florence2**: Job SLURM in coda (2600524)
- 🔄 **Idefics3**: Job SLURM running su patagarro (2600523)

**IMPORTANTE**: Usare SEMPRE gli script con suffisso `_REAL_colors` per inference corretta con immagini colorate!

---

## 📊 SCRIPT EVALUATION CORRETTI E SISTEMATI - 2025-07-04

### 🧹 PULIZIA SCRIPT EVALUATION:
- ❌ **24 script obsoleti eliminati** (baseline vecchi, gemma specifici, test utility)
- ✅ **4 script corretti mantenuti** (universali e aggiornati)

### ✅ SCRIPT EVALUATION CORRETTI MANTENUTI:

#### 🔧 **Script Universale:**
- ✅ `scripts/evaluation/evaluate_any_model.py`
  - **Uso**: Valuta qualsiasi modello con tutte le metriche
  - **Dataset**: `baseline_t7_corrected_400_REAL_colors_fixed.json` (immagini colorate)
  - **Metriche**: BLEU-1,2,3,4, METEOR, CIDEr, CLIPScore, Diversity
  - **Output**: JSON metriche + radar chart automatico
  - **Comando**: `python scripts/evaluation/evaluate_any_model.py --results_file path/to/results.json --model_name "ModelName"`

#### 🎯 **Script Modelli in Training:**
- ✅ `scripts/evaluation/evaluate_trained_models_t7.py`
  - **Uso**: Valuta modelli T8/T9 trained (Gemma/Llama)
  - **Dataset**: `test_set_100k_final_10000.json` (10K esempi training)
  - **Metriche**: BLEU-1,2,3,4, METEOR, CIDEr, Diversity (NO CLIPScore - no immagini PNG)
  - **Output**: JSON risultati per ogni modello
  - **Comando**: `python scripts/evaluation/evaluate_trained_models_t7.py --gemma_model_path path/to/gemma --llama_model_path path/to/llama`

#### 📊 **Script Metriche Specifiche:**
- ✅ `scripts/evaluation/calculate_gemma_metrics.py`
  - **Uso**: Calcola metriche specifiche per Gemma
  - **Dataset**: Risultati Gemma esistenti

#### 📚 **Documentazione:**
- ✅ `scripts/evaluation/README_evaluation.md`
  - **Contenuto**: Guida completa utilizzo script evaluation

### 🔧 CORREZIONI APPLICATE:

#### **Dataset Corretti per Tipo Modello:**
- **Modelli Training (T8/T9)**: `test_set_100k_final_10000.json` ✅
  - 10,000 esempi dal dataset di training
  - Solo XML SVG + caption (no immagini PNG)
  - Metriche NLP standard (no CLIPScore)

- **Modelli Baseline**: `baseline_t7_corrected_400_REAL_colors_fixed.json` ✅
  - 400 esempi con immagini colorate corrette
  - XML SVG + caption + image_path
  - Tutte le metriche incluso CLIPScore

#### **Path Immagini Corretti:**
- **Training models**: `image_base_path=None` (no immagini PNG)
- **Baseline models**: `baseline_t7_images_colors_fixed/` (immagini colorate)

### 📋 ESEMPI UTILIZZO:

#### **Valutazione Modelli Training (quando completati):**
```bash
# Valuta Gemma T9 e Llama T8
python scripts/evaluation/evaluate_trained_models_t7.py \
    --gemma_model_path experiments/xml_direct_input/outputs/gemma_t9_no_accumulation \
    --llama_model_path experiments/xml_direct_input/outputs/llama_t8_24h \
    --max_examples 400

# Output: evaluation_results/t7_models_evaluation_TIMESTAMP/
#   - gemma_t7_results.json
#   - llama_t7_results.json
#   - t7_evaluation_results.json (completo)
```

#### **Valutazione Qualsiasi Modello:**
```bash
# Valuta risultati BLIP-2 con confronto baseline
python scripts/evaluation/evaluate_any_model.py \
    --results_file evaluation_results/blip2_REAL_colors_results_20250704_134206.json \
    --model_name "BLIP-2_Colors" \
    --output_dir evaluation_results/blip2_analysis/ \
    --compare_with evaluation_results/baseline_metrics_colors_fixed_20250704_130450.json

# Output:
#   - BLIP-2_Colors_metrics_TIMESTAMP.json
#   - BLIP-2_Colors_radar_chart.png/pdf
```

### ❌ SCRIPT OBSOLETI ELIMINATI:
- `baseline_inference_hf_official.py`
- `calculate_baseline_metrics_100.py`
- `calculate_metrics_with_clip.py`
- `clip_metrics_calculator.py`
- `create_baseline_fixed_report.py`
- `create_baseline_qualitative_report.py`
- `create_baseline_radar_final.py`
- `create_final_radar_chart.py`
- `create_final_t7_radar_chart.py`
- `create_html_qualitative_report.py`
- `evaluate_baseline_corrected.py`
- `evaluate_t7_models.py`
- `generate_gemma_only_report.py`
- `generate_individual_radar_charts.py`
- `generate_qualitative_report.py`
- `generate_qualitative_report_with_gemma.py`
- `generate_radar_chart_with_gemma.py`
- `run_gemma_evaluation_pipeline.py`
- `open_report.py`
- `quick_qualitative_examples.py`
- `run_baseline_inference_colors_fixed.py`
- `test_clip_score.py`
- `example_usage.sh`
- `README_EVALUATION.md` (duplicato)

### 🎯 SISTEMA EVALUATION FINALE:
- ✅ **Script universali** per qualsiasi modello
- ✅ **Dataset corretti** per tipo modello (training vs baseline)
- ✅ **Metriche appropriate** (con/senza CLIPScore)
- ✅ **Immagini colorate** per baseline
- ✅ **Output standardizzato** (JSON + radar chart)
- ✅ **Documentazione completa**

**SISTEMA EVALUATION PRONTO PER MODELLI TRAINING COMPLETATI!** 🚀

---

## 🎯 CODICE GEMMA2_9B_IT_TEST3_MULTI_GPU_OPTIMIZED (SCORE 0.3019)

### 📋 INFORMAZIONI MODELLO:
- **Nome Run**: gemma2_9b_it_test3_multi_gpu_optimized
- **Score WandB**: 0.3019 (CLIP Score o metrica simile)
- **Modello Base**: google/gemma-2-9b-it (Instruction Tuned)
- **Checkpoint**: experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/checkpoint-36200
- **Architettura**: Gemma 2 9B con LoRA multi-GPU ottimizzato

### 🔧 CONFIGURAZIONE TRAINING CORRETTA:

#### **Script Training**: `scripts/training/train_lora_multi_gpu_simple.py`
#### **Configurazione JSON**: `experiments/xml_direct_input/configs/gemma2_9b_it_test3_multi_gpu_optimized.json`

**⚠️ CORREZIONE IMPORTANTE**: Il modello con score 0.3019 usava **GRADIENT ACCUMULATION**!

```json
{
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 16,     # IMPORTANTE: CON accumulation!
    "effective_batch_size": 32,            # Batch size effettivo più grande
    "learning_rate": 1e-4,                 # Learning rate ottimizzato
    "lr_scheduler_type": "cosine_with_restarts",
    "warmup_ratio": 0.05,
    "weight_decay": 0.01,
    "max_grad_norm": 0.5,

    "lora_r": 64,                          # LoRA rank alto
    "lora_alpha": 128,                     # LoRA alpha (α/r = 2.0)
    "lora_target_modules": [
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    "lora_dropout": 0.1,

    "max_steps": 45000,                    # Target 36,200 steps
    "eval_steps": 500,
    "save_steps": 250,
    "logging_steps": 50,

    "use_flash_attention_2": true,         # Flash Attention 2.0
    "sliding_window_attention": 4096,      # Local attention
    "soft_capping": 50.0,                  # Attention capping
    "final_logit_softcapping": 30.0        # Output capping
}
```

#### **Comando Training Multi-GPU CORRETTO**:
```bash
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma2_9b_it_test3_multi_gpu_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu \
    --disable_quantization \
    --use_wandb \
    --wandb_project "svg_captioning" \
    --wandb_run_name "gemma2_9b_it_test3_multi_gpu_optimized"
```

### 🎛️ CONFIGURAZIONE SLURM:

#### **Script SLURM**: `scripts/slurm/gemma2_9b_it_test3_multi_gpu.slurm`
```bash
#!/bin/bash
#SBATCH --job-name=gemma2_9b_it_test3_multi_gpu
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/gemma2_9b_it_test3_multi_gpu_%j.out
#SBATCH --error=logs/gemma2_9b_it_test3_multi_gpu_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 GEMMA 2 9B IT TEST3 MULTI-GPU OPTIMIZED"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri ottimizzati per Gemma 2 9B
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma2_9b_it_test3_multi_gpu_optimized
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu"
mkdir -p "$OUTPUT_DIR"

echo "✅ Avvio GEMMA 2 9B IT MULTI-GPU OPTIMIZED..."

# Training con configurazione ottimizzata
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma2_9b_it_test3_multi_gpu_optimized

echo "🏁 GEMMA 2 9B IT TRAINING COMPLETATO"
echo "End time: $(date)"
echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
```

### 📊 RISULTATI PERFORMANCE:
- **WandB Score**: 0.3019 (probabilmente CLIP Score)
- **Checkpoint Finale**: checkpoint-36200 (36,200 steps)
- **Architettura**: Gemma 2 9B IT con LoRA rank 64
- **Training Time**: ~24 ore su dual-GPU boost_usr_prod
- **Memory Usage**: ~28-30GB per GPU (richiede >24GB)

### 🔧 CARATTERISTICHE TECNICHE:
- **Flash Attention 2.0**: Per efficienza memoria
- **Soft Capping**: Stabilità numerica (50.0 attention, 30.0 output)
- **Sliding Window**: 4096 tokens per attenzione locale
- **Quantization**: 4-bit con BitsAndBytes
- **Distributed**: NCCL backend su 2 GPU
- **Optimizer**: AdamW fused per performance

**⚠️ IMPORTANTE: Questo è il codice del modello con score 0.3019 su WandB**

---

# FASE 9: CODICE COMPLETO TRAINING 3 MODELLI + GEMMA2_9B_IT_TEST3_MULTI_GPU_OPTIMIZED

## 🔧 MODEL ARCHITECTURE DETAILS

### GEMMA T9 (google/gemma-2-9b-it)

| Parametro | Gemma T9 |
|-----------|----------|
| Nome modello | google/gemma-2-9b-it |
| Parametri totali | 9.24B |
| Architettura | Gemma-2 |
| Context Length | 8,192 token |
| Vocab Size | 256,000 |
| Layers | 42 |
| Attention Heads | 16 |
| Hidden Size | 3,584 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |

### LLAMA T8 (meta-llama/Llama-3.1-8B-Instruct)

| Parametro | Llama T8 |
|-----------|----------|
| Nome modello | meta-llama/Llama-3.1-8B-Instruct |
| Parametri totali | 8.03B |
| Architettura | Llama-3.1 |
| Context Length | 131,072 token |
| Vocab Size | 128,256 |
| Layers | 32 |
| Attention Heads | 32 |
| Hidden Size | 4,096 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |

### LLAMA T9 (meta-llama/Llama-3.1-8B-Instruct)

| Parametro | Llama T9 |
|-----------|----------|
| Nome modello | meta-llama/Llama-3.1-8B-Instruct |
| Parametri totali | 8.03B |
| Architettura | Llama-3.1 |
| Context Length | 131,072 token |
| Vocab Size | 128,256 |
| Layers | 32 |
| Attention Heads | 32 |
| Hidden Size | 4,096 |
| Intermediate Size | 14,336 |
| Precision | bfloat16 |

---

## 🎯 TRAINING CONFIGURATION

### GEMMA T9

| Parametro | Gemma T9 |
|-----------|----------|
| Max Steps | 45,000 |
| Eval Steps | 500 |
| Save Steps | 250 |
| Logging Steps | 50 |
| Max Eval Samples | 10,000 |
| Max Length (token) | 2,048 |
| Gradient Accumulation | 1 |
| Batch Size per device | 2 |
| Effective Batch Size | 4 (2 GPU) |
| Num Train Epochs | 3 |

### LLAMA T8

| Parametro | Llama T8 |
|-----------|----------|
| Max Steps | 45,000 |
| Eval Steps | 500 |
| Save Steps | 250 |
| Logging Steps | 50 |
| Max Eval Samples | 10,000 |
| Max Length (token) | 2,048 |
| Gradient Accumulation | 1 |
| Batch Size per device | 2 |
| Effective Batch Size | 4 (2 GPU) |
| Num Train Epochs | 3 |

### LLAMA T9

| Parametro | Llama T9 |
|-----------|----------|
| Max Steps | 45,000 |
| Eval Steps | 500 |
| Save Steps | 250 |
| Logging Steps | 50 |
| Max Eval Samples | 10,000 |
| Max Length (token) | 2,048 |
| Gradient Accumulation | 1 |
| Batch Size per device | 2 |
| Effective Batch Size | 4 (2 GPU) |
| Num Train Epochs | 3 |

---

## ⚙️ OPTIMIZER / LR SCHEDULING

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| Optimizer | AdamW |
| Learning Rate | 5e-5 |
| LR Scheduler | Cosine |
| Warmup Steps | 500 |
| Warmup Ratio | 0.011 |
| Weight Decay | 0.01 |
| Adam Beta1 | 0.9 |
| Adam Beta2 | 0.999 |
| Max Grad Norm | 1.0 |

---

## 🔧 LoRA CONFIGURATION

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| LoRA Rank | 16 |
| LoRA Alpha | 32 |
| LoRA Dropout | 0.1 |
| Target Modules | q_proj, k_proj, v_proj, o_proj |
| LoRA Bias | None |
| PEFT Type | LORA |
| Task Type | CAUSAL_LM |

---

## 💾 QUANTIZZAZIONE

### TUTTI I MODELLI (CONFIGURAZIONE IDENTICA)

| Parametro | Valore |
|-----------|--------|
| Load in 4bit | True |
| Compute Dtype | float16 |
| Double Quant | True |
| Quant Type | nf4 |
| Quant Storage | uint8 |

---

## 🖥️ HARDWARE E INFRASTRUTTURA

### TUTTI I MODELLI

| Parametro | Valore |
|-----------|--------|
| GPU | 2x GPU (NCCL backend) |
| Distribuito | NCCL backend |
| CPU Cores | 8 |
| RAM | 48GB |
| Partizione SLURM | boost_usr_prod |
| Account SLURM | tesi_ediluzio |

---

## 📋 DATASET CONFIGURATION

| Parametro | Valore |
|-----------|--------|
| File Training | train_set_100k_final_90000.json (90,000 esempi) |
| File Validation | test_set_100k_final_10000.json (10,000 esempi) |
| Formato Input | XML SVG ottimizzato + caption in linguaggio naturale |
| Max SVG Length | 1,800 caratteri |
| Max Caption Length | 200 caratteri |
| Preprocessing | Tokenizzazione con padding dinamico |

---

## 🎯 CHAT TEMPLATE

### GEMMA T9
```
<bos><start_of_turn>user
Generate a detailed caption for this SVG image:

{xml_content}
<end_of_turn>
<start_of_turn>model
```

### LLAMA T8/T9
```
<|begin_of_text|><|start_header_id|>user<|end_header_id|>

Generate a detailed caption for this SVG image:

{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>
```

---

## 📁 FILE PYTHON TRAINING

### 🔧 SCRIPT PRINCIPALE: `scripts/training/train_lora_multi_gpu_simple.py`

**Percorso**: `/work/tesi_ediluzio/scripts/training/train_lora_multi_gpu_simple.py`

**Caratteristiche**:
- Multi-GPU distributed training con NCCL
- LoRA fine-tuning con PEFT
- Quantizzazione 4-bit con BitsAndBytes
- WandB logging integrato
- Early stopping e checkpoint management
- Supporto Gemma 2 e Llama 3.1

**Comando Esecuzione**:
```bash
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path MODEL_NAME \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path CONFIG_PATH \
    --output_dir OUTPUT_DIR
```

### 🔧 SCRIPT RESUME: `scripts/training/train_lora_resume_fixed.py`

**Percorso**: `/work/tesi_ediluzio/scripts/training/train_lora_resume_fixed.py`

**Caratteristiche**:
- Resume automatico da checkpoint esistenti
- Patch per bug PEFT active_adapters
- Compatibilità con versioni multiple di transformers
- Gestione errori memoria CUDA

---

## 📁 FILE CONFIGURAZIONE JSON

### 🔧 GEMMA T9: `experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json`

**Percorso**: `/work/tesi_ediluzio/experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json`

**Configurazione Chiave**:
```json
{
    "model_name_or_path": "google/gemma-2-9b-it",
    "per_device_train_batch_size": 1,
    "gradient_accumulation_steps": 1,
    "learning_rate": 5e-5,
    "max_steps": 50000,
    "lora_r": 64,
    "lora_alpha": 128,
    "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    "load_in_4bit": true,
    "fp16": true
}
```

### 🔧 LLAMA T8: `experiments/xml_direct_input/configs/llama_t8_final_optimized.json`

**Percorso**: `/work/tesi_ediluzio/experiments/xml_direct_input/configs/llama_t8_final_optimized.json`

**Configurazione Chiave**:
```json
{
    "model_name_or_path": "meta-llama/Llama-3.1-8B-Instruct",
    "per_device_train_batch_size": 2,
    "gradient_accumulation_steps": 1,
    "learning_rate": 5e-5,
    "max_steps": 45000,
    "lora_r": 16,
    "lora_alpha": 32,
    "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj"],
    "load_in_4bit": true,
    "fp16": true
}
```

### 🔧 LLAMA T9: `experiments/xml_direct_input/configs/llama_t9_no_accumulation.json`

**Percorso**: `/work/tesi_ediluzio/experiments/xml_direct_input/configs/llama_t9_no_accumulation.json`

**Configurazione Chiave**:
```json
{
    "model_name_or_path": "meta-llama/Llama-3.1-8B-Instruct",
    "per_device_train_batch_size": 2,
    "gradient_accumulation_steps": 1,
    "learning_rate": 5e-5,
    "max_steps": 45000,
    "lora_r": 16,
    "lora_alpha": 32,
    "lora_target_modules": ["q_proj", "k_proj", "v_proj", "o_proj"],
    "load_in_4bit": true,
    "fp16": true
}
```

---

## 📁 FILE SLURM SCRIPTS

### 🔧 GEMMA T9: `scripts/slurm/FINAL_gemma_t9_24h.slurm`

**Percorso**: `/work/tesi_ediluzio/scripts/slurm/FINAL_gemma_t9_24h.slurm`

```bash
#!/bin/bash
#SBATCH --job-name=FINAL_gemma_t9_24h
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FINAL_gemma_t9_24h_%j.out
#SBATCH --error=logs/FINAL_gemma_t9_24h_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FINAL GEMMA T9 24H - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new che funziona
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri ESATTI come l'originale Gemma T9
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export HF_HUB_DISABLE_TELEMETRY=1
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory - TRAINING DA ZERO
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_no_accumulation"

echo "📂 Training DA ZERO in: $OUTPUT_DIR"

# Crea directory se non esiste
mkdir -p "$OUTPUT_DIR"

echo "✅ Directory pronta per training da zero"

echo "✅ Avvio FINAL GEMMA T9 24H..."

# Lancia training con MODELLO 9B CORRETTO come l'originale
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29502 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 FINAL GEMMA T9 24H COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory non trovata"
fi
```

### 🔧 LLAMA T8: `scripts/slurm/FINAL_llama_t8_24h.slurm`

**Percorso**: `/work/tesi_ediluzio/scripts/slurm/FINAL_llama_t8_24h.slurm`

```bash
#!/bin/bash
#SBATCH --job-name=FINAL_llama_t8_24h
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FINAL_llama_t8_24h_%j.out
#SBATCH --error=logs/FINAL_llama_t8_24h_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FINAL LLAMA T8 24H - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new che funziona
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri ESATTI come l'originale
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export HF_HUB_DISABLE_TELEMETRY=1
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory - TRAINING DA ZERO
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"

echo "📂 Training DA ZERO in: $OUTPUT_DIR"

# Crea directory se non esiste
mkdir -p "$OUTPUT_DIR"

echo "✅ Directory pronta per training da zero"

echo "✅ Avvio FINAL LLAMA T8 24H..."

# Lancia training con TORCHRUN come l'originale che ha funzionato
torchrun --nproc_per_node=2 \
    --master_port=29501 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb

EXIT_CODE=$?

echo ""
echo "🏁 FINAL LLAMA T8 24H COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory non trovata"
fi
```

### 🔧 LLAMA T9: `scripts/slurm/FINAL_llama_t9_24h.slurm`

**Percorso**: `/work/tesi_ediluzio/scripts/slurm/FINAL_llama_t9_24h.slurm`

```bash
#!/bin/bash
#SBATCH --job-name=FINAL_llama_t9_24h
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/FINAL_llama_t9_24h_%j.out
#SBATCH --error=logs/FINAL_llama_t9_24h_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 FINAL LLAMA T9 24H - Job ID: $SLURM_JOB_ID"
echo "Start time: $(date)"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva ambiente svg_env_new che funziona
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔍 Verifica ambiente svg_env_new..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}')"

# Parametri ESATTI come l'originale T9
export WANDB_DISABLED=true
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export DISABLE_MLFLOW_INTEGRATION=TRUE
export HF_HUB_DISABLE_TELEMETRY=1
export TRANSFORMERS_OFFLINE=0
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory - TRAINING DA ZERO
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t9_no_accumulation"

echo "📂 Training DA ZERO in: $OUTPUT_DIR"

# Crea directory se non esiste
mkdir -p "$OUTPUT_DIR"

echo "✅ Directory pronta per training da zero"

echo "✅ Avvio FINAL LLAMA T9 24H..."

# Lancia training con configurazione ESATTA T9 originale
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29503 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t9_no_accumulation.json \
    --output_dir "$OUTPUT_DIR"

EXIT_CODE=$?

echo ""
echo "🏁 FINAL LLAMA T9 24H COMPLETATO"
echo "Exit code: $EXIT_CODE"
echo "End time: $(date)"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Training completato con successo"
    echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
    ls -la "$OUTPUT_DIR"
else
    echo "❌ Training fallito con codice: $EXIT_CODE"
fi

# Verifica checkpoint finali
if [ -d "$OUTPUT_DIR" ]; then
    echo "📊 Checkpoint disponibili:"
    find "$OUTPUT_DIR" -name "checkpoint-*" -type d | sort -V | tail -5
else
    echo "❌ Output directory non trovata"
fi
```

---

## 📁 CHECKPOINT LOCATIONS E PROTEZIONE

### 🔒 DIRECTORY CHECKPOINT (DA PROTEGGERE SEMPRE)

| Modello | Directory Checkpoint | Pattern |
|---------|---------------------|---------|
| **Gemma T9** | `experiments/xml_direct_input/outputs/gemma_t9_no_accumulation/` | `checkpoint-XXXX/` |
| **Llama T8** | `experiments/xml_direct_input/outputs/llama_t8_24h/` | `checkpoint-XXXX/` |
| **Llama T9** | `experiments/xml_direct_input/outputs/llama_t9_no_accumulation/` | `checkpoint-XXXX/` |
| **Gemma2 Test3** | `experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/` | `checkpoint-36200/` |

### 🛡️ REGOLE PROTEZIONE CHECKPOINT

**⚠️ REGOLE ASSOLUTE:**
1. **NON ELIMINARE MAI** i checkpoint senza conferma esplicita dell'utente
2. **TENERE SEMPRE** almeno gli ultimi 3 checkpoint per modello
3. **BACKUP AUTOMATICO** dei checkpoint ogni 1000 steps
4. **VERIFICA ESISTENZA** prima di qualsiasi operazione di pulizia
5. **LOG DETTAGLIATO** di ogni operazione sui checkpoint

### 📊 CHECKPOINT SALVATI OGNI

| Parametro | Valore |
|-----------|--------|
| Save Steps | 250 steps |
| Eval Steps | 500 steps |
| Logging Steps | 50 steps |
| Save Total Limit | 5 checkpoint |
| Best Model | Salvato automaticamente |

---

## 🚀 COMANDI ESECUZIONE

### 🔧 AVVIO TRAINING SINGOLO

```bash
# Gemma T9
cd /work/tesi_ediluzio
sbatch scripts/slurm/FINAL_gemma_t9_24h.slurm

# Llama T8
cd /work/tesi_ediluzio
sbatch scripts/slurm/FINAL_llama_t8_24h.slurm

# Llama T9
cd /work/tesi_ediluzio
sbatch scripts/slurm/FINAL_llama_t9_24h.slurm
```

### 🔧 AVVIO TRAINING MULTIPLO

```bash
# Avvia tutti e 3 i training in sequenza
cd /work/tesi_ediluzio
sbatch scripts/slurm/FINAL_gemma_t9_24h.slurm
sbatch scripts/slurm/FINAL_llama_t8_24h.slurm
sbatch scripts/slurm/FINAL_llama_t9_24h.slurm
```

### 🔧 MONITORAGGIO

```bash
# Controlla stato job
squeue -u ediluzio

# Controlla log in tempo reale
tail -f logs/FINAL_*_24h_*.out

# Verifica checkpoint
ls -la experiments/xml_direct_input/outputs/*/checkpoint-*
```

**🎯 FINE FASE 9 - TUTTI I FILE E CONFIGURAZIONI SALVATI**
